import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Typography } from 'components/typography/Typography';
import { Button } from 'components/ui/button';
import { useTranslation } from 'react-i18next';

type IdCardReaderPendingComponentProps = {
  onCancel: () => void;
  isCancelButtonVisible?: boolean;
};

const IdCardReaderPendingComponent = ({
  onCancel,
  isCancelButtonVisible = true,
}: IdCardReaderPendingComponentProps) => {
  const { t } = useTranslation(LocizeNamespaces.common);
  return (
    <>
      <div className="flex flex-col gap-4 items-center justify-center">
        <Typography variant="xs" className="text-center">
          {t(LOCIZE_COMMON_TRANSLATION_KEYS.idCardReaderPendingHeading)}
        </Typography>
        <Typography variant="text-m" className="text-center">
          {t(LOCIZE_COMMON_TRANSLATION_KEYS.idCardReaderPendingDisclaimer)}
        </Typography>
      </div>

      {isCancelButtonVisible && (
        <Button
          className="w-full mt-8"
          onClick={onCancel}
          variant="transparent"
        >
          {t(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
      )}
    </>
  );
};

export default IdCardReaderPendingComponent;
