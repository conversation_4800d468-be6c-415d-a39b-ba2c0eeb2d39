import { AppSearchParams } from 'app-constants';
import { Button, type ButtonProps } from 'components/ui/button';
import { useRootContext } from 'context/root';
import { useLogout } from 'hooks';
import { type PropsWithChildren, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { removeURLParameters, replacePathWithLogin } from 'services';

export const LogoutButton = ({
  children,
  ...props
}: PropsWithChildren & ButtonProps) => {
  const [logOutLoading, setLogOutLoading] = useState(false);
  const navigate = useNavigate();
  const { pathname, search } = useLocation();
  const { quietUserRefetch } = useRootContext();
  const { logOut } = useLogout();

  const onLogOutClick = async () => {
    try {
      setLogOutLoading(true);
      await logOut();
      if (quietUserRefetch) {
        await quietUserRefetch();
      }

      const cleanUrl = removeURLParameters({
        url: `${pathname}${search}`,
        parameters: [AppSearchParams.payseraLoginPaymentStatus],
      });

      navigate(replacePathWithLogin(cleanUrl));
    } catch (error) {
      toast.error('Error during logout');
      console.error(error);
    } finally {
      setLogOutLoading(false);
    }
  };
  return (
    <Button loading={logOutLoading} onClick={onLogOutClick} {...props}>
      {children}
    </Button>
  );
};
