import { HirePurchaseRoutePaths } from 'app-constants';
import type { AnyObject } from 'models/core.models';

export const HirePurchaseStepCount: AnyObject = {
  [HirePurchaseRoutePaths.CHECKOUT]: 0,
  [HirePurchaseRoutePaths.CONTACT]: 1,
  [HirePurchaseRoutePaths.PURPOSE_OF_LOAN]: 2,
  [HirePurchaseRoutePaths.CONTACT_EXTRA]: 3,
  [HirePurchaseRoutePaths.ACCOUNT_SCORING]: 4,
  [HirePurchaseRoutePaths.INCOME_VERIFICATION]: 5,
  [HirePurchaseRoutePaths.EMTA_CONSENT]: 6,
  [HirePurchaseRoutePaths.SIGNING]: 7,
};

export const HirePurchaseWithProgressBarRoutePaths = [
  HirePurchaseRoutePaths.CHECKOUT,
  HirePurchaseRoutePaths.CONTACT,
  HirePurchaseRoutePaths.PURPOSE_OF_LOAN,
  HirePurchaseRoutePaths.CONTACT_EXTRA,
  HirePurchaseRoutePaths.ACCOUNT_SCORING,
  HirePurchaseRoutePaths.INCOME_VERIFICATION,
  HirePurchaseRoutePaths.EMTA_CONSENT,
];
