import type { HirePurchaseRoutePaths } from 'app-constants';
import { useLocation } from 'react-router-dom';

import {
  HirePurchaseStepCount,
  HirePurchaseWithProgressBarRoutePaths,
} from './config';
import { ProgressBar } from './ProgressBar';

export const HirePurchaseProgressBar = ({
  className,
}: {
  className?: string;
}) => {
  const location = useLocation();
  const [, , pagePath] = location.pathname.split('/');

  const isVisible = HirePurchaseWithProgressBarRoutePaths.includes(
    pagePath as HirePurchaseRoutePaths,
  );

  if (!isVisible) return null;

  const totalStepCount = Object.keys(HirePurchaseStepCount).length;
  const currentStepCount =
    HirePurchaseStepCount[pagePath as HirePurchaseRoutePaths];

  return (
    <ProgressBar
      currentStepCount={currentStepCount}
      totalStepCount={totalStepCount}
      className={className}
    />
  );
};
