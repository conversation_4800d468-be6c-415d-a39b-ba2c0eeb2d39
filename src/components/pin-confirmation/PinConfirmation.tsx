import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Typography } from 'components/typography/Typography';
import { Button } from 'components/ui/button';
import { useTranslation } from 'react-i18next';

type PinConfirmationComponentProps = {
  title?: string;
  pin: string;
  onCancel?: () => void;
  isCancelButtonVisible?: boolean;
};

const PinConfirmationComponent = ({
  pin,
  onCancel,
  isCancelButtonVisible = true,
}: PinConfirmationComponentProps) => {
  const { t } = useTranslation(LocizeNamespaces.common);

  return (
    <div className="flex flex-col gap-4 items-center justify-center">
      <Typography variant="xs" className="text-center">
        {t(LOCIZE_COMMON_TRANSLATION_KEYS.pinConfirmationHeading)}
      </Typography>
      <div className="w-fit">
        <Typography variant="m">{pin}</Typography>
        <div className="h-1 bg-system-green" />
      </div>
      <Typography variant="text-m" className="text-center">
        {t(LOCIZE_COMMON_TRANSLATION_KEYS.pinConfirmationDisclaimer)}
      </Typography>

      {isCancelButtonVisible && (
        <Button
          className="w-full mt-8"
          onClick={onCancel}
          variant="transparent"
        >
          {t(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
      )}
    </div>
  );
};

export default PinConfirmationComponent;
