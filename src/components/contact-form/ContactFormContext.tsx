import type { Option } from 'models';
import { createContext, useContext } from 'react';

export interface ContactFormContextValue {
  visiblePageAttributes: Record<string, boolean>;
  userInfoValidationErrors: Record<string, boolean>;
  politicalExposureOptions: Option[];
  showPoliticalExposureInfo?: boolean;
  linkClassName?: string;
}

const ContactFormContext = createContext<ContactFormContextValue | null>(null);

export const ContactFormProvider = ContactFormContext.Provider;

export const useContactFormContext = () => {
  const context = useContext(ContactFormContext);
  if (!context) {
    throw new Error(
      'useContactFormContext must be used within ContactFormProvider',
    );
  }
  return context;
};
