import {
  FormFieldNames,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  PageAttributeNames,
} from 'app-constants';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { CONTACT_INFO } from 'environment';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactFormContext } from '../ContactFormContext';

export const NewsletterField = <
  TFieldValues extends FieldValues = FieldValues,
>() => {
  const { t } = useTranslation('contact');
  const { contactName } = CONTACT_INFO;
  const { control, getFieldState, formState } = useFormContext<TFieldValues>();
  const { visiblePageAttributes, userInfoValidationErrors } =
    useContactFormContext();

  if (!visiblePageAttributes[PageAttributeNames.newsletterCheckbox]) {
    return null;
  }

  return (
    <FormCheckboxField<TFieldValues>
      containerClassName="mt-2.5 px-2.5"
      name={FormFieldNames.newsletterAgreement as Path<TFieldValues>}
      control={control}
      label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.newsletterCheckboxLabel, {
        contact_name: contactName,
      })}
      disabled={formState.isSubmitting}
      invalid={
        userInfoValidationErrors[FormFieldNames.newsletterAgreement] ||
        !!getFieldState(
          FormFieldNames.newsletterAgreement as Path<TFieldValues>,
        ).error
      }
    />
  );
};
