import {
  FormFieldNames,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  PageAttributeNames,
} from 'app-constants';
import { FormInputField } from 'components/form/form-input-field';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactFormContext } from '../ContactFormContext';

export const IbanField = <TFieldValues extends FieldValues = FieldValues>() => {
  const { t } = useTranslation('contact');
  const { control, getFieldState, formState } = useFormContext<TFieldValues>();
  const { visiblePageAttributes, userInfoValidationErrors } =
    useContactFormContext();

  if (!visiblePageAttributes[PageAttributeNames.iban]) {
    return null;
  }

  return (
    <FormInputField<TFieldValues>
      name={FormFieldNames.iban as Path<TFieldValues>}
      control={control}
      label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.ibanFieldLabel)}
      invalid={
        userInfoValidationErrors[FormFieldNames.iban] ||
        !!getFieldState(FormFieldNames.iban as Path<TFieldValues>).error
      }
      disabled={formState.isSubmitting}
    />
  );
};
