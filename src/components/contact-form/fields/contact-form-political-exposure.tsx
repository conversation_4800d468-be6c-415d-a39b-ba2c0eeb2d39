import { PoliticalExposure } from 'api/core/generated';
import {
  FormFieldNames,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  PageAttributeNames,
} from 'app-constants';
import { FormSelectField } from 'components/form/form-select-field/FormSelectField';
import { WarningNotification } from 'components/notification';
import { useEffect } from 'react';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactFormContext } from '../ContactFormContext';

export const PoliticalExposureField = <
  TFieldValues extends FieldValues = FieldValues,
>() => {
  const { t } = useTranslation('contact');
  const { control, getFieldState, formState, watch } =
    useFormContext<TFieldValues>();
  const {
    visiblePageAttributes,
    userInfoValidationErrors,
    politicalExposureOptions,
    showPoliticalExposureInfo,
  } = useContactFormContext();

  const politicalExposureSelectValue = watch(
    FormFieldNames.politicalExposure as Path<TFieldValues>,
  );

  if (!visiblePageAttributes[PageAttributeNames.politicalExposureSelect]) {
    return null;
  }

  return (
    <>
      <FormSelectField<TFieldValues>
        name={FormFieldNames.politicalExposure as Path<TFieldValues>}
        control={control}
        label={t(
          LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonFieldLabel,
        )}
        options={politicalExposureOptions}
        info={
          showPoliticalExposureInfo
            ? t(
                LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonTooltipLabel,
              )
            : undefined
        }
        invalid={
          userInfoValidationErrors[FormFieldNames.politicalExposure] ||
          !!getFieldState(
            FormFieldNames.politicalExposure as Path<TFieldValues>,
          ).error
        }
        disabled={formState.isSubmitting || !politicalExposureOptions.length}
      />

      {politicalExposureSelectValue !== PoliticalExposure.NONE ? (
        <WarningNotification>
          {t(
            LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonDisclaimerLabel,
          )}
        </WarningNotification>
      ) : null}
    </>
  );
};
