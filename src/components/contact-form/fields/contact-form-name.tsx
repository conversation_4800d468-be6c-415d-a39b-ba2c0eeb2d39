import {
  FormFieldNames,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  PageAttributeNames,
} from 'app-constants';
import { FormInputField } from 'components/form/form-input-field';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactFormContext } from '../ContactFormContext';

export const NameField = <TFieldValues extends FieldValues = FieldValues>() => {
  const { t } = useTranslation('contact');
  const { control } = useFormContext<TFieldValues>();
  const { visiblePageAttributes } = useContactFormContext();

  if (!visiblePageAttributes[PageAttributeNames.name]) {
    return null;
  }

  return (
    <FormInputField
      control={control}
      name={FormFieldNames.name as Path<TFieldValues>}
      label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.nameFieldLabel)}
      disabled
    />
  );
};
