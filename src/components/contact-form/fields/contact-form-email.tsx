import {
  FormFieldNames,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  PageAttributeNames,
} from 'app-constants';
import { FormInputField } from 'components/form/form-input-field';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactFormContext } from '../ContactFormContext';

export const EmailField = <
  TFieldValues extends FieldValues = FieldValues,
>() => {
  const { t } = useTranslation('contact');
  const { control, getFieldState, formState } = useFormContext<TFieldValues>();
  const { visiblePageAttributes, userInfoValidationErrors } =
    useContactFormContext();

  if (!visiblePageAttributes[PageAttributeNames.email]) {
    return null;
  }

  return (
    <FormInputField
      control={control}
      name={FormFieldNames.email as Path<TFieldValues>}
      label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.emailFieldLabel)}
      invalid={
        userInfoValidationErrors[FormFieldNames.email] ||
        !!getFieldState(FormFieldNames.email as Path<TFieldValues>).error
      }
      disabled={formState.isSubmitting}
    />
  );
};
