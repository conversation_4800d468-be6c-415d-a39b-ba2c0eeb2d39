import {
  FormFieldNames,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  PageAttributeNames,
} from 'app-constants';
import { FormPhoneField } from 'components/form/form-phone-field';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactFormContext } from '../ContactFormContext';

export const PhoneField = <
  TFieldValues extends FieldValues = FieldValues,
>() => {
  const { t } = useTranslation('contact');
  const { control, getFieldState, formState } = useFormContext<TFieldValues>();
  const { visiblePageAttributes, userInfoValidationErrors } =
    useContactFormContext();

  if (!visiblePageAttributes[PageAttributeNames.phoneNumber]) {
    return null;
  }

  return (
    <FormPhoneField<TFieldValues>
      name={FormFieldNames.phone as Path<TFieldValues>}
      control={control}
      label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.phoneFieldLabel)}
      invalid={
        userInfoValidationErrors[FormFieldNames.phone] ||
        !!getFieldState(FormFieldNames.phone as Path<TFieldValues>).error
      }
      disabled={formState.isSubmitting}
    />
  );
};
