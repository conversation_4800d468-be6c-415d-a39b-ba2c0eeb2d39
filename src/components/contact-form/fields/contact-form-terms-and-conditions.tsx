import {
  FormFieldNames,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  PageAttributeNames,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { CONTACT_INFO } from 'environment';
import type { AnyObject } from 'models';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactFormContext } from '../ContactFormContext';

export const TermsAndConditionsField = <
  TFieldValues extends FieldValues = FieldValues,
>() => {
  const { t, i18n } = useTranslation('contact');
  const { contactName } = CONTACT_INFO;
  const { control, getFieldState, formState } = useFormContext<TFieldValues>();
  const { visiblePageAttributes, userInfoValidationErrors } =
    useContactFormContext();

  if (!visiblePageAttributes[PageAttributeNames.termsAndConditionsCheckbox]) {
    return null;
  }

  const termsLabel = (
    <AppLocalizationComponent
      components={{
        site_link: (
          <AppExternalLink
            openInNewTab
            className="underline cursor-pointer hover:text-primary-brand-02"
            to={(REDIRECT_URLS.termsPageUrs as AnyObject)[i18n.language]}
          />
        ),
      }}
      locizeKey={
        LOCIZE_CONTACT_TRANSLATION_KEYS.termsAndConditionsCheckboxLabel
      }
      t={t}
      values={{
        contact_name: contactName,
      }}
    />
  );

  return (
    <FormCheckboxField<TFieldValues>
      containerClassName="mt-2.5 px-2.5"
      name={FormFieldNames.conditionsAgreement as Path<TFieldValues>}
      control={control}
      label={termsLabel}
      disabled={formState.isSubmitting}
      invalid={
        userInfoValidationErrors[FormFieldNames.conditionsAgreement] ||
        !!getFieldState(
          FormFieldNames.conditionsAgreement as Path<TFieldValues>,
        ).error
      }
    />
  );
};
