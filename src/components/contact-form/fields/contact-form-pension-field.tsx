import {
  FormFieldNames,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  PageAttributeNames,
} from 'app-constants';
import { AppLocalizationComponent } from 'components';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { CONTACT_INFO } from 'environment';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactFormContext } from '../ContactFormContext';

export const PensionField = <
  TFieldValues extends FieldValues = FieldValues,
>() => {
  const { t } = useTranslation('contact');
  const { contactName } = CONTACT_INFO;
  const { control, getFieldState, formState } = useFormContext<TFieldValues>();
  const { visiblePageAttributes, userInfoValidationErrors } =
    useContactFormContext();

  if (!visiblePageAttributes[PageAttributeNames.pension_checkbox]) {
    return null;
  }

  const pensionLabel = (
    <AppLocalizationComponent
      locizeKey={LOCIZE_CONTACT_TRANSLATION_KEYS.pensionCheckboxLabel}
      t={t}
      values={{
        contact_name: contactName,
      }}
    />
  );

  return (
    <FormCheckboxField<TFieldValues>
      containerClassName="mt-2.5 px-2.5"
      name={FormFieldNames.allowPensionQuery as Path<TFieldValues>}
      control={control}
      label={pensionLabel}
      disabled={formState.isSubmitting}
      invalid={
        userInfoValidationErrors[FormFieldNames.allowPensionQuery] ||
        !!getFieldState(FormFieldNames.allowPensionQuery as Path<TFieldValues>)
          .error
      }
    />
  );
};
