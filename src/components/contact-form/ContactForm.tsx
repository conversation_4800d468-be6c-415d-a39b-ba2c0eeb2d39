import {
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { useRootContext } from 'context/root';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  type ContactFormContextValue,
  ContactFormProvider,
} from './ContactFormContext';
import {
  AddressField,
  CityField,
  EmailField,
  IbanField,
  NameField,
  NewsletterField,
  PensionField,
  PhoneField,
  PoliticalExposureField,
  PostalCodeField,
  TermsAndConditionsField,
} from './fields';

export interface ContactFormProps<
  TFieldValues extends FieldValues = FieldValues,
> {
  form: UseFormReturn<TFieldValues>;
  onSubmit: (
    data: TFieldValues,
    form?: UseFormReturn<TFieldValues>,
  ) => void | Promise<void>;
  contextValue: ContactFormContextValue;
  submitButtonDisabledLogic?: (
    visiblePageAttributes: Record<string, boolean>,
    formValues: TFieldValues,
  ) => boolean;
  passFormToSubmit?: boolean;
  conditionsAgreement?: boolean;
}

export const ContactForm = <TFieldValues extends FieldValues = FieldValues>({
  form,
  onSubmit,
  contextValue,
  submitButtonDisabledLogic,
  conditionsAgreement,
  passFormToSubmit = false,
}: ContactFormProps<TFieldValues>) => {
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();

  const termsAndConditionsCheckboxValue = form.watch(
    FormFieldNames.conditionsAgreement as any,
  );

  const pensionCheckboxValue = form.watch(
    FormFieldNames.allowPensionQuery as any,
  );

  const isTermsConsentSatisfied =
    !!conditionsAgreement || !!termsAndConditionsCheckboxValue;
  const isPensionConsentSatisfied =
    !contextValue.visiblePageAttributes[PageAttributeNames.pension_checkbox] ||
    !!pensionCheckboxValue;

  const defaultSubmitButtonDisabled =
    !isTermsConsentSatisfied || !isPensionConsentSatisfied;

  const submitButtonDisabled = submitButtonDisabledLogic
    ? submitButtonDisabledLogic(
        contextValue.visiblePageAttributes,
        form.getValues(),
      )
    : defaultSubmitButtonDisabled;

  const handleSubmit = async (data: TFieldValues) => {
    try {
      if (passFormToSubmit) {
        await onSubmit(data, form);
      } else {
        await onSubmit(data);
      }
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <ContactFormProvider value={contextValue}>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="grid w-full gap-2"
        >
          <NameField<TFieldValues> />
          <EmailField<TFieldValues> />
          <PhoneField<TFieldValues> />
          <AddressField<TFieldValues> />
          <CityField<TFieldValues> />
          <PostalCodeField<TFieldValues> />
          <PoliticalExposureField<TFieldValues> />
          <IbanField<TFieldValues> />
          {!conditionsAgreement && <TermsAndConditionsField<TFieldValues> />}
          <PensionField<TFieldValues> />
          <NewsletterField<TFieldValues> />

          <Button
            className="mt-8"
            disabled={
              submitButtonDisabled ||
              form.formState.isSubmitting ||
              pageUrlAndNavigationProcessing
            }
            loading={
              form.formState.isSubmitting || pageUrlAndNavigationProcessing
            }
            type="submit"
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
          </Button>
          <Button
            fullWidth
            className="mt-2"
            variant="white"
            disabled={form.formState.isSubmitting}
            onClick={() => {
              getPageUrlAndNavigate(false);
            }}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
          </Button>
        </form>
      </Form>
    </ContactFormProvider>
  );
};
