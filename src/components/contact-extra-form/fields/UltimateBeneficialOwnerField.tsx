import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import type { ContactExtraFormType } from 'hooks/page-logic/credit-line';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactExtraFormContext } from '../ContactExtraFormContext';

export const UltimateBeneficialOwnerField = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<ContactExtraFormType>();
  const { isSubmitting } = useFormState({ control });
  const { visiblePageAttributes, validationErrors } =
    useContactExtraFormContext();

  if (!visiblePageAttributes[PageAttributeNames.ultimateBeneficialOwner]) {
    return null;
  }

  return (
    <FormCheckboxField
      containerClassName="mt-2 px-2.5"
      control={control}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerLabel,
      )}
      name={FormFieldNames.ultimateBeneficialOwner}
      info={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerTooltip,
      )}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.ultimateBeneficialOwner] ||
        !!getFieldState(FormFieldNames.ultimateBeneficialOwner).error
      }
    />
  );
};
