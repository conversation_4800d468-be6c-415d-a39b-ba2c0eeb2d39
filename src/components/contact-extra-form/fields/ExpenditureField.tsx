import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { ContactExtraFormType } from 'hooks/page-logic/credit-line';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactExtraFormContext } from '../ContactExtraFormContext';

export const ExpenditureField = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<ContactExtraFormType>();
  const { isSubmitting } = useFormState({ control });
  const { visiblePageAttributes, validationErrors } =
    useContactExtraFormContext();

  if (!visiblePageAttributes[PageAttributeNames.expenditureMonthly]) {
    return null;
  }

  return (
    <FormNumberInputField
      control={control}
      disabled={isSubmitting}
      name={FormFieldNames.expenditureMonthly}
      label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureLabel)}
      info={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureTooltipLabel)}
      invalid={
        validationErrors[FormFieldNames.expenditureMonthly] ||
        !!getFieldState(FormFieldNames.expenditureMonthly).error
      }
      suffix={'€'}
    />
  );
};
