import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { ContactExtraFormType } from 'hooks/page-logic/credit-line';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactExtraFormContext } from '../ContactExtraFormContext';

export const OverdueDebtField = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState, watch, setValue } =
    useFormContext<ContactExtraFormType>();
  const { isSubmitting } = useFormState({ control });
  const { visiblePageAttributes, validationErrors } =
    useContactExtraFormContext();

  if (!visiblePageAttributes[PageAttributeNames.overdueDebt]) {
    return null;
  }

  return (
    <CheckboxWrapper
      className="mt-2"
      checked={
        !!watch(FormFieldNames.overdueDebt) ||
        validationErrors[FormFieldNames.overdueDebt]
      }
      onCheckedChange={(checked) => {
        if (!checked) {
          setValue(FormFieldNames.overdueDebt, null);
        }
      }}
      label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtCheckboxLabel)}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.overdueDebt] ||
        !!getFieldState(FormFieldNames.overdueDebt).error
      }
    >
      <FormNumberInputField
        control={control}
        name={FormFieldNames.overdueDebt}
        label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtLabel)}
        info={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtTooltipLabel)}
        disabled={isSubmitting}
        invalid={
          validationErrors[FormFieldNames.overdueDebt] ||
          !!getFieldState(FormFieldNames.overdueDebt).error
        }
        suffix={'€'}
      />
    </CheckboxWrapper>
  );
};
