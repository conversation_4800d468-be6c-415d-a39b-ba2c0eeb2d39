import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { ContactExtraFormType } from 'hooks/page-logic/credit-line';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactExtraFormContext } from '../ContactExtraFormContext';

export const NumberOfDependentsField = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<ContactExtraFormType>();
  const { isSubmitting } = useFormState({ control });
  const { visiblePageAttributes, validationErrors } =
    useContactExtraFormContext();

  if (!visiblePageAttributes[PageAttributeNames.numberOfDependents]) {
    return null;
  }

  return (
    <FormNumberInputField
      control={control}
      name={FormFieldNames.numberOfDependents}
      label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.dependentsLabel)}
      allowLeadingZeros
      fixedDecimalScale={true}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.numberOfDependents] ||
        !!getFieldState(FormFieldNames.numberOfDependents).error
      }
    />
  );
};
