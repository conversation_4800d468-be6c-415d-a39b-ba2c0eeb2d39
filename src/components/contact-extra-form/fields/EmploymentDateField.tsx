import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormSelectField } from 'components/form/form-select-field';
import type { ContactExtraFormType } from 'hooks/page-logic/credit-line';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactExtraFormContext } from '../ContactExtraFormContext';

export const EmploymentDateField = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<ContactExtraFormType>();
  const { isSubmitting } = useFormState({ control });
  const { visiblePageAttributes, validationErrors, employmentDateOptions } =
    useContactExtraFormContext();

  if (!visiblePageAttributes[PageAttributeNames.employmentDate]) {
    return null;
  }

  return (
    <FormSelectField
      control={control}
      name={FormFieldNames.employmentDate}
      label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.employmentDateLabel)}
      options={employmentDateOptions}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.employmentDate] ||
        !!getFieldState(FormFieldNames.employmentDate).error
      }
    />
  );
};
