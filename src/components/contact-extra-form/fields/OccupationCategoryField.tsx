import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormSelectField } from 'components/form/form-select-field';
import type { ContactExtraFormType } from 'hooks/page-logic/credit-line';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactExtraFormContext } from '../ContactExtraFormContext';

export const OccupationCategoryField = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<ContactExtraFormType>();
  const { isSubmitting } = useFormState({ control });
  const { visiblePageAttributes, validationErrors, occupationCategoryOptions } =
    useContactExtraFormContext();

  if (!visiblePageAttributes[PageAttributeNames.occupationCategoryDropdown]) {
    return null;
  }

  return (
    <FormSelectField
      control={control}
      name={FormFieldNames.occupationCategory}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.occupationCategoryFieldLabel,
      )}
      options={occupationCategoryOptions}
      invalid={
        validationErrors[FormFieldNames.occupationCategory] ||
        !!getFieldState(FormFieldNames.occupationCategory).error
      }
      disabled={isSubmitting || !occupationCategoryOptions.length}
    />
  );
};
