import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { ContactExtraFormType } from 'hooks/page-logic/credit-line';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactExtraFormContext } from '../ContactExtraFormContext';

export const PlanningNewDebtsField = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState, watch, setValue } =
    useFormContext<ContactExtraFormType>();
  const { isSubmitting } = useFormState({ control });
  const { visiblePageAttributes, validationErrors } =
    useContactExtraFormContext();

  if (!visiblePageAttributes[PageAttributeNames.planningNewDebts]) {
    return null;
  }

  return (
    <CheckboxWrapper
      className="mt-2"
      checked={
        !!watch(FormFieldNames.planningNewDebts) ||
        validationErrors[FormFieldNames.planningNewDebts]
      }
      onCheckedChange={(checked) => {
        if (!checked) {
          setValue(FormFieldNames.planningNewDebts, null);
        }
      }}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsCheckboxLabel,
      )}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.planningNewDebts] ||
        !!getFieldState(FormFieldNames.planningNewDebts).error
      }
    >
      <FormNumberInputField
        control={control}
        name={FormFieldNames.planningNewDebts}
        label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsLabel)}
        disabled={isSubmitting}
        invalid={
          validationErrors[FormFieldNames.planningNewDebts] ||
          !!getFieldState(FormFieldNames.planningNewDebts).error
        }
        suffix={'€'}
      />
    </CheckboxWrapper>
  );
};
