import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { ContactExtraFormType } from 'hooks/page-logic/credit-line';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useContactExtraFormContext } from '../ContactExtraFormContext';

export const FutureReducedEarningsField = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState, watch, setValue } =
    useFormContext<ContactExtraFormType>();
  const { isSubmitting } = useFormState({ control });
  const { visiblePageAttributes, validationErrors } =
    useContactExtraFormContext();

  if (!visiblePageAttributes[PageAttributeNames.futureReducedEarnings]) {
    return null;
  }

  return (
    <CheckboxWrapper
      className="mt-2"
      checked={
        !!watch(FormFieldNames.futureReducedEarnings) ||
        validationErrors[FormFieldNames.futureReducedEarnings]
      }
      onCheckedChange={(checked) => {
        if (!checked) {
          setValue(FormFieldNames.futureReducedEarnings, null);
        }
      }}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsCheckboxLabel,
      )}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.futureReducedEarnings] ||
        !!getFieldState(FormFieldNames.futureReducedEarnings).error
      }
    >
      <FormNumberInputField
        control={control}
        disabled={isSubmitting}
        name={FormFieldNames.futureReducedEarnings}
        label={t(
          LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsLabel,
        )}
        invalid={
          validationErrors[FormFieldNames.futureReducedEarnings] ||
          !!getFieldState(FormFieldNames.futureReducedEarnings).error
        }
        suffix={'€'}
      />
    </CheckboxWrapper>
  );
};
