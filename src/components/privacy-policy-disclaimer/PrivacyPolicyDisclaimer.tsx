import {
  AppLanguages,
  AppRegions,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Typography } from 'components/typography';
import { region } from 'environment';
import { Trans, useTranslation } from 'react-i18next';

// Privacy policy URLs by region and language
const PRIVACY_POLICY_URLS = {
  [AppRegions.et]: {
    [AppLanguages.en]: 'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=en',
    [AppLanguages.et]: 'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=et',
    [AppLanguages.ru]: 'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=ru',
    [AppLanguages.lv]: 'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=en',
    [AppLanguages.lt]: 'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=en',
  },
  [AppRegions.lv]: {
    [AppLanguages.en]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=en',
    [AppLanguages.et]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=en',
    [AppLanguages.ru]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=ru',
    [AppLanguages.lv]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=lv',
    [AppLanguages.lt]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=en',
  },
  [AppRegions.lt]: {
    [AppLanguages.en]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=en',
    [AppLanguages.et]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=en',
    [AppLanguages.ru]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=ru',
    [AppLanguages.lv]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=en',
    [AppLanguages.lt]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=lt',
  },
};

export const PrivacyPolicyDisclaimer = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.login);

  const currentLanguage = i18n.language as AppLanguages;
  const currentRegion = region as AppRegions;

  // Get the privacy policy URL for the current region and language
  const privacyPolicyUrl =
    PRIVACY_POLICY_URLS[currentRegion]?.[currentLanguage] ||
    PRIVACY_POLICY_URLS[AppRegions.et][AppLanguages.en]; // Fallback to EE English

  return (
    <div className="mt-4 text-center">
      <Typography variant="text-s" affects="medium" className="text-gray-600">
        <Trans
          i18nKey={LOCIZE_LOGIN_TRANSLATION_KEYS.privacyPolicyDisclaimer}
          t={t}
          components={{
            privacyPolicyLink: (
              <a
                href={privacyPolicyUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="underline"
              />
            ),
          }}
        />
      </Typography>
    </div>
  );
};
