import {
  FloatingFormLabel,
  FormControl,
  FormField,
  FormItem,
} from 'components/ui/form';
import { Input } from 'components/ui/input';
import type { ReactNode } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { NumericFormat } from 'react-number-format';
import { cn } from 'utils/tailwind';

type FormNumberInputFieldProps<TFieldValues extends FieldValues = FieldValues> =
  {
    name: Path<TFieldValues>;
    control: Control<TFieldValues>;
    label: ReactNode;
    info?: string;
    className?: string;
    containerClassName?: string;
    disabled?: boolean;
    allowNegative?: boolean;
    allowLeadingZeros?: boolean;
    fixedDecimalScale?: boolean;
    suffix?: string;
    invalid?: boolean;
    onBlur?: () => void;
    onChanged?: (value?: Nullable<number>) => void;
  };

export const FormNumberInputField = <
  TFieldValues extends FieldValues = FieldValues,
>({
  name,
  control,
  label,
  info,
  className,
  containerClassName,
  disabled,
  invalid,
  allowNegative = false,
  allowLeadingZeros = false,
  fixedDecimalScale = false,
  suffix,
  onBlur,
  onChanged,
}: FormNumberInputFieldProps<TFieldValues>) => (
  <FormField
    control={control}
    name={name}
    render={({ field: { onChange, onBlur: onBlurDefault, value } }) => (
      <FormItem className={cn('w-full relative', containerClassName)}>
        <FormControl>
          <div className="relative">
            <NumericFormat
              disabled={disabled}
              invalid={invalid}
              allowLeadingZeros={allowLeadingZeros}
              allowNegative={allowNegative}
              className={cn('w-full', className)}
              customInput={Input}
              fixedDecimalScale={fixedDecimalScale}
              name={`${name}-input`}
              onValueChange={({ floatValue, value }) => {
                const finalValue = value === '' ? null : floatValue;
                onChange(finalValue);
                onChanged?.(finalValue);
              }}
              onBlur={onBlur ?? onBlurDefault}
              value={value ?? ''}
              info={info}
              suffix={suffix ? ` ${suffix}` : undefined}
            />
            <FloatingFormLabel
              filled={value !== null && value !== undefined && value !== ''}
            >
              {label}
            </FloatingFormLabel>
          </div>
        </FormControl>
      </FormItem>
    )}
  />
);
