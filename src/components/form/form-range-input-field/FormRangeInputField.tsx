import { Currencies } from 'app-constants';
import { FormField, FormItem } from 'components/ui/form';
import type { SliderSize } from 'components/ui/slider';
import { Slider } from 'components/ui/slider';
import { useDebounce, useGlobalDragEnd } from 'hooks/utils';
import MinusIcon from 'icons/minus-icon.svg?react';
import PlusIcon from 'icons/plus-icon.svg?react';
import { useEffect } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import Skeleton from 'react-loading-skeleton';
import { NumericFormat } from 'react-number-format';
import { cn } from 'utils/tailwind';

import { FormNumberInputField } from '../form-number-input-field';

export enum FormRangeInputFieldDefaultValueOutputFormat {
  FORM_INPUT = 'form-input',
  INPUT = 'input',
}

type FormRangeInputFieldProps<TFieldValues extends FieldValues = FieldValues> =
  {
    control: Control<TFieldValues>;
    name: Path<TFieldValues>;
    min: number;
    max: number;
    step?: number;
    onAmountButtonsClick?: (operation: 'inc' | 'dec') => () => void;
    onAmountInputBlur?: () => void;
    loading?: boolean;
    disabled?: boolean;
    className?: string;
    customValueOutput?: React.ReactNode;
    onAmountChange?: (formFieldValues: FieldValues) => void;
    onSliderDragEnd?: (formFieldValues: FieldValues) => void;
    size?: SliderSize;
    customInput?: React.ReactNode;
    withInput?: boolean;
    defaultValueOutputFormat?: FormRangeInputFieldDefaultValueOutputFormat;
    outputValueInputLabel?: string;
  };

export const FormRangeInputField = <
  TFieldValues extends FieldValues = FieldValues,
>({
  control,
  name,
  min,
  max,
  step = 10,
  loading,
  disabled,
  className,
  customValueOutput,
  size = 'regular',
  onAmountChange,
  onSliderDragEnd,
  withInput,
  defaultValueOutputFormat = FormRangeInputFieldDefaultValueOutputFormat.INPUT,
  outputValueInputLabel,
}: FormRangeInputFieldProps<TFieldValues>) => {
  const { setIsDragging, dragEndValue, setDragEndValue } = useGlobalDragEnd();

  return (
    <FormField
      control={control}
      name={name}
      render={({
        field: { onChange, value, name, ...field },
        fieldState: { error },
      }) => {
        const isDecButtonDisabled = value <= min || disabled;
        const isIncButtonDisabled = value >= max || disabled;

        const debouncedOnAmountChange = useDebounce(
          (value?: Nullable<number>) => {
            onAmountChange?.({ [name]: value });
          },
          300,
        );

        const debouncedOnDragEnd = useDebounce((value?: Nullable<number>) => {
          onSliderDragEnd?.({ [name]: value });
        }, 300);

        useEffect(() => {
          if (dragEndValue !== null && onSliderDragEnd) {
            debouncedOnDragEnd(value);
            setDragEndValue(null);
          }
        }, [dragEndValue, value, debouncedOnDragEnd, onSliderDragEnd]);

        const handleAmountButtonsClick = (operation: 'inc' | 'dec') => () => {
          const amountValue = value ?? 0;
          const newValue =
            operation === 'inc' ? amountValue + step : amountValue - step;
          onChange(newValue);
          debouncedOnAmountChange(newValue);
        };

        const handleSliderDragStart = () => {
          setIsDragging(true);
        };

        const handleAmountInputBlur = () => {
          if (!value) {
            onChange(min);
            return;
          }

          if (value < min) {
            onChange(min);
            return;
          }

          if (value > max) {
            onChange(max);
            return;
          }

          if (value % step !== 0) {
            onChange((Math.ceil(value / step) * step).toFixed(0));
            return;
          }
        };

        const renderButtons = () => (
          <>
            <button
              type="button"
              onClick={handleAmountButtonsClick('dec')}
              disabled={isDecButtonDisabled}
              className={cn(
                'cursor-pointer',
                isDecButtonDisabled && 'opacity-40 cursor-not-allowed',
              )}
            >
              <MinusIcon />
            </button>
            <button
              type="button"
              onClick={handleAmountButtonsClick('inc')}
              disabled={isIncButtonDisabled}
              className={cn(
                'cursor-pointer',
                isIncButtonDisabled && 'opacity-40 cursor-not-allowed',
              )}
            >
              <PlusIcon />
            </button>
          </>
        );

        const getDefaultValueOutput = () => {
          if (
            defaultValueOutputFormat ===
            FormRangeInputFieldDefaultValueOutputFormat.FORM_INPUT
          ) {
            return (
              <FormNumberInputField
                onChanged={debouncedOnAmountChange}
                control={control}
                name={name}
                label={outputValueInputLabel}
                disabled={disabled}
                invalid={!!error}
                allowNegative={false}
                allowLeadingZeros={false}
                fixedDecimalScale={true}
                suffix={Currencies.euro}
              />
            );
          }

          return (
            <NumericFormat
              {...field}
              className={cn(
                !withInput &&
                  'text-[2rem] px-0 font-bold leading-9 tracking-[-.12rem] w-full rounded-md focus:outline-none',
                error && 'text-red-500 placeholder:text-red-500',
              )}
              allowLeadingZeros={false}
              allowNegative={false}
              fixedDecimalScale
              name={`${name}-input`}
              onValueChange={(v) => {
                const newValue = v.floatValue === undefined ? 0 : v.floatValue;
                onChange(newValue);
              }}
              suffix={` ${Currencies.euro}`}
              onBlur={handleAmountInputBlur}
              thousandSeparator=","
              value={value || ''}
              isAllowed={(values) => {
                const { formattedValue } = values;
                if (formattedValue.startsWith('0')) {
                  return false;
                }
                return true;
              }}
              disabled={disabled}
            />
          );
        };

        const valueOutput = customValueOutput
          ? customValueOutput
          : getDefaultValueOutput();

        return (
          <FormItem className={className}>
            <div className="grid grid-cols-[1fr_auto]  items-center mb-4">
              {loading ? (
                <Skeleton className="rounded-3xl w-full h-9 animate-pulse-opacity" />
              ) : (
                <>
                  {valueOutput}
                  <div className="flex gap-4">
                    {withInput ? null : renderButtons()}
                  </div>
                </>
              )}
            </div>
            {loading ? (
              <Skeleton
                containerClassName="w-full"
                className={cn(
                  'rounded-3xl w-full h-9.5 animate-pulse-opacity ',
                )}
              />
            ) : (
              <div className="flex gap-4">
                <Slider
                  {...field}
                  max={max}
                  min={min}
                  name={`${name}-slider`}
                  onValueChange={(value) => {
                    onChange(+value[0].toFixed(0));
                  }}
                  onPointerDown={handleSliderDragStart}
                  onTouchStart={handleSliderDragStart}
                  step={step}
                  value={[value]}
                  disabled={disabled}
                  size={size}
                />
                {withInput ? renderButtons() : null}
              </div>
            )}
          </FormItem>
        );
      }}
    />
  );
};
