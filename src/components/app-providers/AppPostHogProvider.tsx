import type { Children } from 'models';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import { initializePostHog, isPostHogEnabled } from 'services/posthog-service';

initializePostHog();

// Ensure PostHog is available globally after initialization
if (typeof window !== 'undefined' && isPostHogEnabled) {
  window.PH = posthog;
}

export const AppPostHogProvider = ({ children }: Children) => {
  if (!isPostHogEnabled) {
    return children;
  }

  return <PostHogProvider client={posthog}>{children}</PostHogProvider>;
};
