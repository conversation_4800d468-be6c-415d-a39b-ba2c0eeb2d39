import type { SuspenseQueryHookOptions } from '@apollo/client';
import type {
  GetConsumerLoanScheduleSettingsQuery,
  GetConsumerLoanScheduleSettingsQueryVariables,
} from 'api/core/generated';
import {
  type ScheduleTypeSettings,
  useGetConsumerLoanScheduleSettingsSuspenseQuery,
} from 'api/core/generated';
import { AppApiVersions } from 'app-constants';

export const useGetConsumerLoanScheduleSettingsSuspense = (
  options?: SuspenseQueryHookOptions<
    GetConsumerLoanScheduleSettingsQuery,
    GetConsumerLoanScheduleSettingsQueryVariables
  >,
) => {
  const { data, error } = useGetConsumerLoanScheduleSettingsSuspenseQuery({
    context: {
      apiVersion: AppApiVersions.core,
    },
    ...options,
  });

  return {
    scheduleTypeSettings: (data?.schedule_type_settings ??
      null) as ScheduleTypeSettings | null,
    possiblePeriods:
      data?.schedule_type_settings?.possible_periods?.filter(
        (month): month is number => typeof month === 'number' && month > 0,
      ) ?? [],
    minLoanAmount: data?.schedule_type_settings?.min_loan_amount ?? 0,
    maxLoanAmount: data?.schedule_type_settings?.max_loan_amount ?? 0,
    errorLoadingScheduleTypeSettings: error,
  };
};
