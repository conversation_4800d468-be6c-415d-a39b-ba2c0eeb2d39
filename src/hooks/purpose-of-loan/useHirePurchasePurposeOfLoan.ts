import { useRootContext } from 'context/root';
import { useEffectOnce, useGetCurrentApplication } from 'hooks';
import { formatApplicationToPurposeOfLoanPageDataFormat } from 'services';

import type { PurposeOfLoanPageData } from '../../components/purpose-of-loan';

export const useHirePurchasePurposeOfLoan = () => {
  const { getPageUrlAndNavigate } = useRootContext();
  const { getApplication, application } = useGetCurrentApplication();

  const data: PurposeOfLoanPageData =
    formatApplicationToPurposeOfLoanPageDataFormat(application);

  const handleSubmit = async () => {
    await getPageUrlAndNavigate(true);
  };

  const handleBack = () => {
    getPageUrlAndNavigate(false);
  };

  useEffectOnce(() => {
    getApplication();
  });

  return {
    data,
    onSubmit: handleSubmit,
    onBack: handleBack,
  };
};
