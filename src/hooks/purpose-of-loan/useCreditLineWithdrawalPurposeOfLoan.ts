import { useRootContext } from 'context/root';
import { useEffectOnce, useGetCreditLineWithdrawalByHash } from 'hooks';
import { formatCreditLineWithdrawalToPurposeOfLoanPageDataFormat } from 'services';

import type { PurposeOfLoanPageData } from '../../components/purpose-of-loan';

export const useCreditLineWithdrawalPurposeOfLoan = () => {
  const { getPageUrlAndNavigate } = useRootContext();
  const { getCreditLineWithdrawal, creditLineWithdrawal } =
    useGetCreditLineWithdrawalByHash();

  const data: PurposeOfLoanPageData =
    formatCreditLineWithdrawalToPurposeOfLoanPageDataFormat(
      creditLineWithdrawal,
    );

  const handleSubmit = async () => {
    await getPageUrlAndNavigate(true);
  };

  const handleBack = () => {
    getPageUrlAndNavigate(false);
  };

  useEffectOnce(() => {
    getCreditLineWithdrawal();
  });

  return {
    data,
    onSubmit: handleSubmit,
    onBack: handleBack,
  };
};
