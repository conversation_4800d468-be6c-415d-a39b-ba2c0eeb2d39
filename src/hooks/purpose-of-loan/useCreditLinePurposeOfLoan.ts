import { useRootContext } from 'context/root';
import { formatUserToPurposeOfLoanPageDataFormat } from 'services';

import type { PurposeOfLoanPageData } from '../../components/purpose-of-loan';

export const useCreditLinePurposeOfLoan = () => {
  const { user, quietUserRefetch, getPageUrlAndNavigate } = useRootContext();

  const data: PurposeOfLoanPageData =
    formatUserToPurposeOfLoanPageDataFormat(user);

  const handleSubmit = async () => {
    await quietUserRefetch();
    await getPageUrlAndNavigate(true);
  };

  const handleBack = () => {
    getPageUrlAndNavigate(false);
  };

  return {
    data,
    onSubmit: handleSubmit,
    onBack: handleBack,
  };
};
