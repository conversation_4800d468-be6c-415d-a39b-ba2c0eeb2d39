import { useRootContext } from 'context/root';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { formatApplicationToPurposeOfLoanPageDataFormat } from 'services';

import type { PurposeOfLoanPageData } from '../../components/purpose-of-loan';

export const useSmallLoanPurposeOfLoan = () => {
  const { application, quietApplicationRefetch } =
    useGetCurrentApplicationSuspense();
  const { getPageUrlAndNavigate } = useRootContext();

  const data: PurposeOfLoanPageData =
    formatApplicationToPurposeOfLoanPageDataFormat(application);

  const handleSubmit = async () => {
    await quietApplicationRefetch();
    await getPageUrlAndNavigate(true);
  };

  const handleBack = () => {
    getPageUrlAndNavigate(false);
  };

  return {
    data,
    onSubmit: handleSubmit,
    onBack: handleBack,
  };
};
