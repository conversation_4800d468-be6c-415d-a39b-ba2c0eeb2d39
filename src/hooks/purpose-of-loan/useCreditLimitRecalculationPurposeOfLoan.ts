import { useRootContext } from 'context/root';
import { useEffectOnce, useGetCreditLimitRecalculation } from 'hooks';
import { formatCreditLimitRecalculationToPurposeOfLoanPageDataFormat } from 'services';

import type { PurposeOfLoanPageData } from '../../components/purpose-of-loan';

export const useCreditLimitRecalculationPurposeOfLoan = () => {
  const { getPageUrlAndNavigate } = useRootContext();
  const { getCreditLimitRecalculation, creditLimitRecalculation } =
    useGetCreditLimitRecalculation();

  const data: PurposeOfLoanPageData =
    formatCreditLimitRecalculationToPurposeOfLoanPageDataFormat(
      creditLimitRecalculation,
    );

  const handleSubmit = async () => {
    await getPageUrlAndNavigate(true);
  };

  const handleBack = () => {
    getPageUrlAndNavigate(false);
  };

  useEffectOnce(() => {
    getCreditLimitRecalculation();
  });

  return {
    data,
    onSubmit: handleSubmit,
    onBack: handleBack,
  };
};
