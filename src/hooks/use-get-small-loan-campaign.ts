import type { Application } from 'api/core/generated';
import { SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN } from 'app-constants';

type SmallLoanCampaignInfo = {
  offersKeys: string[];
};

export const useGetSmallLoanCampaign = (
  application: Application | null | undefined,
): SmallLoanCampaignInfo | null => {
  if (!application?.campaign) {
    return null;
  }

  const offersKeys: string[] = [];

  const { campaign, credit_info } = application;

  const interestFreeMonths = campaign?.interest_free_months ?? 0;
  const fixedZeroManagementFeeDisclaimer =
    credit_info?.fixed_management_fee == 0;
  const fixedZeroContractFeeDisclaimer = credit_info?.fixed_contract_fee == 0;

  if (interestFreeMonths) {
    if (interestFreeMonths <= 1) {
      offersKeys.push(
        SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.INTEREST_FREE_1_MONTH,
      );
    } else {
      offersKeys.push(
        SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.INTEREST_FREE_MANY_MONTHS,
      );
    }
  }

  if (fixedZeroManagementFeeDisclaimer && fixedZeroContractFeeDisclaimer) {
    offersKeys.push(SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.VARIANT_1);
  }

  if (fixedZeroContractFeeDisclaimer || fixedZeroManagementFeeDisclaimer) {
    offersKeys.push(SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.DEFAULT);
  }

  return {
    offersKeys,
  };
};
