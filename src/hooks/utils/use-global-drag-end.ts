import { useEffect, useState } from 'react';

export const useGlobalDragEnd = () => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragEndValue, setDragEndValue] = useState<number | null>(null);

  useEffect(() => {
    const handleGlobalDragEnd = () => {
      if (isDragging) {
        setIsDragging(false);
        setDragEndValue(Date.now());
      }
    };

    if (isDragging) {
      document.addEventListener('pointerup', handleGlobalDragEnd);
      document.addEventListener('touchend', handleGlobalDragEnd);
      document.addEventListener('mouseup', handleGlobalDragEnd);

      return () => {
        document.removeEventListener('pointerup', handleGlobalDragEnd);
        document.removeEventListener('touchend', handleGlobalDragEnd);
        document.removeEventListener('mouseup', handleGlobalDragEnd);
      };
    }
  }, [isDragging]);

  return {
    isDragging,
    setIsDragging,
    dragEndValue,
    setDragEndValue,
  };
};
