import {
  type CreditSettingCalculator,
  type CreditSettingsQuery,
  type CreditSettingsQueryVariables,
  useCreditSettingsLazyQuery,
} from 'api/core/generated';
import { AppApiVersions } from 'app-constants';
import { useEffect, useState } from 'react';

export const useGetCreditSettings = () => {
  const [creditSettingsData, setCreditSettingsData] =
    useState<CreditSettingsQuery | null>(null);
  const [creditSettingsRequest, { data, loading, error, refetch }] =
    useCreditSettingsLazyQuery({
      context: { apiVersion: AppApiVersions.core },
      notifyOnNetworkStatusChange: true,
    });

  const getCreditSettings = (variables: CreditSettingsQueryVariables) =>
    creditSettingsRequest({ variables });

  const refetchCreditSettings = (variables: CreditSettingsQueryVariables) =>
    refetch(variables);

  useEffect(() => {
    if (data && !loading) {
      setCreditSettingsData(data);
    }
  }, [data, loading]);

  return {
    getCreditSettings,
    creditSettings:
      creditSettingsData?.settings as Array<CreditSettingCalculator> | null,
    creditSettingsLoading: loading,
    creditSettingsError: error,
    refetchCreditSettings,
    initialCreditSettingsLoading: loading && !creditSettingsData,
  };
};
