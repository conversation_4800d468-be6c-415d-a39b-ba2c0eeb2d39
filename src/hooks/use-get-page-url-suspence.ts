import {
  NetworkStatus,
  type SuspenseQ<PERSON>yHookOptions,
  useApolloClient,
} from '@apollo/client';
import {
  PageUrlDocument,
  type PageUrlQuery,
  type PageUrlQueryVariables,
  usePageUrlSuspenseQuery,
} from 'api/purchase-flow/generated';
import { AppApiVersions } from 'app-constants';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

const CACHE_KEY = 'page_url';

export const useGetPageUrlSuspense = (
  options?: SuspenseQueryHookOptions<PageUrlQuery, PageUrlQueryVariables>,
) => {
  const location = useLocation();
  const client = useApolloClient();
  const url = `${location.pathname}${location.search}`;

  const { data, error, networkStatus } = usePageUrlSuspenseQuery({
    context: { apiVersion: AppApiVersions.purchaseFlow },
    fetchPolicy: 'network-only',
    variables: { url, next: null },
    ...options,
  });

  const handlePageUrlQueryCompleted = (responseData = data) => {
    const externalUrl = responseData?.page_url?.external_url;
    if (externalUrl) {
      window.location.href = externalUrl;
      return;
    }

    // Write to cache to track the page url navigation result history
    client.writeQuery({
      query: PageUrlDocument,
      variables: {
        url: CACHE_KEY,
        next: null,
      },
      data: {
        page_url: responseData?.page_url,
      },
    });

    // HANDLE INTERNAL REDIRECT
    const nextPageUrl = responseData?.page_url?.url;
    if (nextPageUrl && nextPageUrl !== url) {
      window.history.pushState({}, '', nextPageUrl);
      window.dispatchEvent(new PopStateEvent('popstate'));
    }
  };

  return {
    handlePageUrlQueryCompleted,
    pageUrl: data?.page_url?.url,
    pageUrlLoading: networkStatus === NetworkStatus.loading,
    pageUrlNetworkStatus: networkStatus,
    pageUrlError: error,
  };
};

export type UseGetPageUrlSuspenseReturn = ReturnType<
  typeof useGetPageUrlSuspense
>;

export const usePageUrlSuspenseState = () => {
  const client = useApolloClient();
  const url = `${location.pathname}${location.search}`;
  const isRootPath = url.split('/').length === 2;

  const [state, setState] = useState<{
    loading: boolean;
    data: PageUrlQuery | undefined;
  }>({ loading: false, data: undefined });

  useEffect(() => {
    if (!isRootPath) {
      const observableQuery = client.watchQuery({
        query: PageUrlDocument,
        variables: { url, next: null },
        context: { apiVersion: AppApiVersions.purchaseFlow },
        fetchPolicy: 'cache-only',
      });

      const subscription = observableQuery.subscribe(({ data, loading }) => {
        if (data?.page_url) {
          setState({ loading, data });
        }
      });

      return () => {
        subscription.unsubscribe();
      };
    }
  }, [client, url, isRootPath]);

  return state;
};
