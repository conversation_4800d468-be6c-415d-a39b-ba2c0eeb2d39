import { zodResolver } from '@hookform/resolvers/zod';
import { PoliticalExposure } from 'api/core/generated';
import {
  FormFieldNames,
  GoogleAnalyticsEvents,
  LOCIZE_USER_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { regionPhonePrefix } from 'environment';
import { useGetPageAttributesSuspense, useGetPageUrl } from 'hooks';
import { useGetPoliticalExposuresDefault } from 'hooks/use-get-political-exposures-default';
import { useLogCreditAccountAction } from 'hooks/use-log-credit-account-action';
import { useUpdateUserInfo } from 'hooks/use-update-user-info';
import { useUpdateUserTerms } from 'hooks/use-update-user-terms';
import type { Option } from 'models';
import { useEffect, useMemo } from 'react';
import { type FieldValues, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  convertPageAttributeNamesToObject,
  extractValidationErrors,
  filterObjectByExistingKeysInObject,
  formatUserToContactPageDataFormat,
} from 'services';
import { processGqlFormValidationErrors } from 'utils/parseGraphQLError';
import * as z from 'zod';

const ContactFormSchema = z.object({
  [FormFieldNames.name]: z.string(),
  [FormFieldNames.email]: z.string().email(),
  [FormFieldNames.phone]: z.string(),
  [FormFieldNames.address]: z.string(),
  [FormFieldNames.city]: z.string(),
  [FormFieldNames.postCode]: z.string(),
  [FormFieldNames.iban]: z.string(),
  [FormFieldNames.politicalExposure]: z.string(),
  [FormFieldNames.conditionsAgreement]: z.boolean(),
  [FormFieldNames.conditionsAndPensionAgreement]: z.boolean(),
  [FormFieldNames.allowPensionQuery]: z.boolean(),
  [FormFieldNames.newsletterAgreement]: z.boolean(),
});

export type ContactFormType = z.infer<typeof ContactFormSchema>;

export const useContactPageLogic = () => {
  const { t } = useTranslation(LocizeNamespaces.user);

  const { pageAttributes } = useGetPageAttributesSuspense();

  const { trackGoogleAnalyticsEvent, quietUserRefetch, user } =
    useRootContext();

  const { getPageUrlAndNavigate } = useGetPageUrl();

  const { logAction } = useLogCreditAccountAction();

  const { updateUserInfo, userInfoError } = useUpdateUserInfo();
  const { updateUserTerms } = useUpdateUserTerms();

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const { politicalExposures } = useGetPoliticalExposuresDefault({
    skip: !visiblePageAttributes[PageAttributeNames.politicalExposureSelect],
  });

  const userInfoValidationErrors = extractValidationErrors(userInfoError);

  const {
    fullName,
    email,
    phone,
    address,
    city,
    postCode,
    iban,
    politicalExposure,
    applicationUserInfoId,
    userId,
    productId,
    newsletterAgreement,
    conditionsAgreement,
    phoneAreaCode,
    allowPensionQuery,
  } = useMemo(() => formatUserToContactPageDataFormat(user), [user]);

  const form = useForm<ContactFormType>({
    resolver: zodResolver(ContactFormSchema),
    defaultValues: {
      [FormFieldNames.name]: fullName ?? '',
      [FormFieldNames.email]: email ?? undefined,
      [FormFieldNames.phone]: phone ?? undefined,
      [FormFieldNames.address]: address ?? undefined,
      [FormFieldNames.city]: city ?? undefined,
      [FormFieldNames.postCode]: postCode ?? undefined,
      [FormFieldNames.iban]: iban ?? '',
      [FormFieldNames.politicalExposure]:
        politicalExposure ?? PoliticalExposure.NONE,
      [FormFieldNames.conditionsAgreement]: conditionsAgreement ?? undefined,
      [FormFieldNames.conditionsAndPensionAgreement]:
        (conditionsAgreement && allowPensionQuery) ?? undefined,
      [FormFieldNames.newsletterAgreement]: newsletterAgreement ?? undefined,
      [FormFieldNames.allowPensionQuery]: allowPensionQuery ?? undefined,
    },
  });

  const getPoliticalExposureOptions = (): Array<Option> =>
    politicalExposures?.map((politicalExposure) => ({
      label: t(
        `${LOCIZE_USER_TRANSLATION_KEYS.politicalExposurePrefix}${politicalExposure}`,
      ),
      value: politicalExposure,
    })) ?? [];

  const onContactFormSubmit = async ({
    phone,
    address,
    post_code,
    city,
    email,
    political_exposure,
    conditions_agreement,
    allow_pension_query,
    newsletter_agreement,
    iban,
  }: FieldValues) => {
    try {
      const variablesFilteredByVisiblePageAttributes =
        filterObjectByExistingKeysInObject(
          {
            email,
            phone,
            address,
            post_code,
            city,
            iban,
          },
          form.control._fields,
        );

      const conditionsAgreementValue =
        conditions_agreement ?? conditionsAgreement;
      const allowPensionQueryValue = allow_pension_query ?? allowPensionQuery;

      // Order of operations is important here
      await updateUserTerms({
        user_id: userId,
        newsletter_agreement: newsletter_agreement ?? newsletterAgreement,
        conditions_agreement: conditionsAgreementValue,
        allow_pension_query: allowPensionQueryValue,
        political_exposure,
      });
      await updateUserInfo({
        application_user_info_id: applicationUserInfoId ?? 0,
        ...variablesFilteredByVisiblePageAttributes,
      });

      await quietUserRefetch();

      await getPageUrlAndNavigate(true);

      trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.contactCompleted);
    } catch (error) {
      if ((error as Error)?.message === 'Unauthorized') {
        await getPageUrlAndNavigate(true);
      }
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  useEffect(() => {
    // LOGGING ACTION
    if (productId) {
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.choosingContactInfo,
      });
    }
  }, [productId]);

  return useMemo(
    () => ({
      form,
      conditionsAgreement,
      onContactFormSubmit,
      politicalExposureOptions: getPoliticalExposureOptions(),
      visiblePageAttributes,
      userInfoValidationErrors,
      phonePrefix: phoneAreaCode ? `+${phoneAreaCode}` : regionPhonePrefix,
    }),
    [
      form,
      conditionsAgreement,
      phoneAreaCode,
      userInfoValidationErrors,
      visiblePageAttributes,
      getPoliticalExposureOptions,
      onContactFormSubmit,
    ],
  );
};
