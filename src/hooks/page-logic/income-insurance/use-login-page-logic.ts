import {
  EparakstsAuthorization<PERSON>ethod,
  LoginPollStatus,
} from 'api/core/generated';
import {
  AppLoginMethods,
  AppProductType,
  AppRoutePaths,
  AppSearchParams,
  EPARAKSTS_BANKLINK_LOGIN_PARAMS_BY_PRODUCT_TYPE_MAP,
  IncomeInsuranceRoutePaths,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  LoginPageViewTypes,
  PAYSERA_PAYMENT_STATUSES,
} from 'app-constants';
import {
  DEFAULT_INSURANCE_PROVIDER_TYPE,
  DEFAULT_MAX_INSURANCE_PERIOD,
} from 'app-constants/insurance.constants';
import { useRootContext } from 'context/root';
import {
  useGetInsuranceProviderOptionsSuspense,
  useGetPageUrl,
  useLoginMethodButtons,
  useStoreInsurance,
} from 'hooks';
import { rootInsuranceStore } from 'hooks/root-logic/income-insurance/use-root-logic';
import { useCheckUpdateUserLanguage } from 'hooks/use-check-update-user-language';
import { useEParakstsLogin } from 'hooks/use-eparaksts-login';
import { useGetBanks } from 'hooks/use-get-banklinks';
import { useLoginByBanklink } from 'hooks/use-login-by-banklink';
import { useLoginByIdCard } from 'hooks/use-login-by-id-card';
import { useLoginByMagicLink } from 'hooks/use-login-by-magic-link';
import { useLoginByMobileId } from 'hooks/use-login-by-mobileId';
import { useLoginByPassword } from 'hooks/use-login-by-password';
import { useLoginBySmartId } from 'hooks/use-login-by-smartId';
import { usePayseraLoginPoll } from 'hooks/use-paysera-login-poll';
import { useRudderStack } from 'hooks/use-rudderstack';
import { useEffectOnce, useLocalStorage } from 'hooks/utils';
import type { VisiblePageAttributes } from 'models';
import { useEffect, useState } from 'react';
import type { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  createSearchParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import {
  extractValidationErrors,
  getFilteredUrlSearchParamsObject,
  removeFromStorage,
  replaceOrAddQueryParamsInUrl,
  setToStorage,
  storeEParakstsOriginalUri,
} from 'services';
import { toast } from 'sonner';
import { delay } from 'utils/async-helpers';

export const useLoginPageLogic = () => {
  const { identify } = useRudderStack();
  const [searchParams] = useSearchParams();
  const { search } = useLocation();
  const navigate = useNavigate();
  const checkUpdateUserLanguage = useCheckUpdateUserLanguage();
  const magicLinkToken = searchParams.get(AppSearchParams.magicLinkToken);
  const eParakstsCode = searchParams.get(AppSearchParams.eParakstsCode);
  const payseraLoginPaymentStatus = searchParams.get(
    AppSearchParams.payseraLoginPaymentStatus,
  );
  const applicationReferenceKey = searchParams.get(
    AppSearchParams.referenceKey,
  );

  const { t } = useTranslation(LocizeNamespaces.errors);
  const [storedQueryParams, storeQueryParams] = useLocalStorage<Record<
    string,
    string
  > | null>(LocalStorageKeys.storedQueryParams, null);

  const [pageViewType, setPageViewType] = useState(LoginPageViewTypes.login);
  const [
    successfulLoginCallbacksProcessing,
    setSuccessfulLoginCallbacksProcessing,
  ] = useState(false);

  const { quietUserRefetch } = useRootContext();
  const { getPageUrlAndNavigate } = useGetPageUrl();

  //TODO: move back once we have resolved on the backend (now it's hardcoded Estonian)
  // const { pageAttributes } = useGetPageAttributesSuspense();

  const { storeInsurance } = useStoreInsurance();

  const { insuranceProviderOptions } = useGetInsuranceProviderOptionsSuspense({
    variables: {
      provider_type: DEFAULT_INSURANCE_PROVIDER_TYPE,
      insurance_period: DEFAULT_MAX_INSURANCE_PERIOD,
    },
  });

  const { banklinks } = useGetBanks();
  const { loginByPassword, loginByPasswordProcessing, loginByPasswordError } =
    useLoginByPassword();
  const {
    loginByMagicLink,
    loginByMagicLinkProcessing,
    loginByMagicLinkError,
  } = useLoginByMagicLink();
  const {
    loginByBankLink,
    loginByBanklinkError,
    loginByBankLinkData,
    loginByBanklinkProcessing,
    banklinkLoginRedirectUrl,
    banklinkLoginAcceptUrl,
    banklinkLoginCancelUrl,
    banklinkLoginStatusError,
  } = useLoginByBanklink();
  const {
    startPayseraLoginStatusPolling,
    stopPayseraLoginStatusPolling,
    payseraLoginStatus,
    payseraLoginStatusError,
  } = usePayseraLoginPoll();
  const {
    loginBySmartId,
    loginBySmartIdError,
    smartIdChallengeData,
    getSmartIdState,
    authorizedBySmartId,
    stopSmartIdStatePolling,
    loginBySmartIdProcessing,
    smartIdPollError,
  } = useLoginBySmartId();
  const {
    loginByMobileId,
    loginByMobileIdError,
    mobileIdChallengeData,
    getMobileIdState,
    authorizedByMobileId,
    stopMobileIdStatePolling,
    loginByMobileIdProcessing,
    mobileIdPollError,
  } = useLoginByMobileId();
  const { loginByIdCard, loginByIdCardProcessing, loginByIdCardError } =
    useLoginByIdCard();
  const {
    executeEParakstsLoginChallenge,
    eParakstsLoginChallengeProcessing,
    eParakstsLoginChallengeError,
    eParakstsLoginProcessing,
    loginByEParaksts,
    eParakstsLoginError,
  } = useEParakstsLogin();
  const { loginMethodButtons, selectedLoginMethod } = useLoginMethodButtons();

  //TODO: move back once we have resolved on the backend (now it's hardcoded Estonian)
  // const visiblePageAttributes = useMemo(
  // 	() => convertPageAttributeNamesToObject(pageAttributes),
  // 	[pageAttributes],
  // );

  const visiblePageAttributes = {
    [AppLoginMethods.mobileId]: true,
    [AppLoginMethods.smartId]: true,
    [AppLoginMethods.idCard]: true,
    [AppLoginMethods.password]: true,
  } as VisiblePageAttributes;

  const loginProcessingMap = {
    [AppLoginMethods.mobileId]: loginByMobileIdProcessing,
    [AppLoginMethods.smartId]: loginBySmartIdProcessing,
    [AppLoginMethods.idCard]: loginByIdCardProcessing,
    [AppLoginMethods.password]: loginByPasswordProcessing,
    [AppLoginMethods.banklink]:
      loginByBanklinkProcessing || Boolean(loginByBankLinkData),
    [AppLoginMethods.eParakstsMobile]: eParakstsLoginChallengeProcessing,
    [AppLoginMethods.eParakstsSmartCard]: eParakstsLoginChallengeProcessing,
  };

  const loginErrorsByMethodMap = {
    [AppLoginMethods.mobileId]: loginByMobileIdError,
    [AppLoginMethods.smartId]: loginBySmartIdError,
    [AppLoginMethods.idCard]: loginByIdCardError,
    [AppLoginMethods.password]: loginByPasswordError,
    [AppLoginMethods.banklink]: loginByBanklinkError,
    [AppLoginMethods.eParakstsMobile]: eParakstsLoginChallengeError,
    [AppLoginMethods.eParakstsSmartCard]: eParakstsLoginChallengeError,
  };

  const loginFLowErrorsByMethodMap = {
    [AppLoginMethods.mobileId]: Boolean(
      loginByMobileIdError || mobileIdPollError,
    ),
    [AppLoginMethods.smartId]: Boolean(loginBySmartIdError || smartIdPollError),
    [AppLoginMethods.idCard]: Boolean(loginByIdCardError),
    [AppLoginMethods.password]: Boolean(loginByPasswordError),
    [AppLoginMethods.banklink]:
      Boolean(
        banklinkLoginStatusError ||
          loginByBanklinkError ||
          payseraLoginStatusError ||
          loginByMagicLinkError,
      ) || payseraLoginPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed,
    [AppLoginMethods.eParakstsMobile]: Boolean(
      eParakstsLoginError || eParakstsLoginChallengeError,
    ),
    [AppLoginMethods.eParakstsSmartCard]: Boolean(
      eParakstsLoginError || eParakstsLoginChallengeError,
    ),
  };

  const loginValidationErrors = extractValidationErrors(
    loginErrorsByMethodMap[selectedLoginMethod],
  );

  const loginMethodButtonsConfig = loginMethodButtons.filter(
    ({ method }) => visiblePageAttributes[method],
  );

  const returnToLoginView = () => {
    setPageViewType(LoginPageViewTypes.login);
  };

  const stopLoginPolling = () => {
    if (selectedLoginMethod === AppLoginMethods.smartId) {
      stopSmartIdStatePolling();
    }

    if (selectedLoginMethod === AppLoginMethods.mobileId) {
      stopMobileIdStatePolling();
    }

    returnToLoginView();
  };

  const executeSuccessfulLoginCallbacks = async () => {
    setSuccessfulLoginCallbacksProcessing(true);

    try {
      const user = await quietUserRefetch();
      checkUpdateUserLanguage();
      identify();

      if (!user) {
        throw new Error('User not found during login');
      }

      const defaultInsuranceProviderOption = insuranceProviderOptions?.[0];

      if (!defaultInsuranceProviderOption) {
        throw new Error(
          'Default insurance provider option is missing when redirecting with updated insurance hash',
        );
      }

      const {
        provider_type,
        insurance_period,
        insured_amount,
        premium_amount,
        partner_premium_amount,
      } = defaultInsuranceProviderOption;

      const insuranceData = await storeInsurance({
        variables: {
          provider_type: provider_type || DEFAULT_INSURANCE_PROVIDER_TYPE,
          insurance_period,
          insured_amount,
          premium_amount,
          partner_premium_amount,
          user_id: user.id,
        },
      });

      const insuranceHash = insuranceData.data?.insurance?.hash;

      rootInsuranceStore.storedInsuranceGlobal = insuranceData.data?.insurance;

      if (!insuranceHash) {
        throw new Error(
          'Insurance hash is missing when redirecting with updated insurance hash',
        );
      }

      const customCurrentPageUrl = replaceOrAddQueryParamsInUrl({
        url: `${AppRoutePaths.INCOME_INSURANCE}/${IncomeInsuranceRoutePaths.SIGNING}${search}`,
        params: {
          [AppSearchParams.insuranceHash]: insuranceHash,
        },
      });

      await getPageUrlAndNavigate(null, {
        customCurrentPageUrl,
      });
    } finally {
      setSuccessfulLoginCallbacksProcessing(false);
    }
  };

  const checkBanklinkButtonDisabled = (method: AppLoginMethods) =>
    method === AppLoginMethods.banklink && !banklinks.length;

  const storeQueryParamsIfNeeded = () => {
    const queryParamsToStore = getFilteredUrlSearchParamsObject(
      EPARAKSTS_BANKLINK_LOGIN_PARAMS_BY_PRODUCT_TYPE_MAP[
        AppProductType.CREDIT_LINE
      ],
      searchParams,
    );

    if (Object.keys(queryParamsToStore).length) {
      storeQueryParams(queryParamsToStore);
    }
  };

  const loginViaSmartId = ({ pin }: FieldValues) => {
    return loginBySmartId({
      pin,
    }).then((result) => {
      if (result.data?.challenge?.session_id) {
        setPageViewType(LoginPageViewTypes.pinConfirmation);
        getSmartIdState({
          variables: { session_id: result.data.challenge.session_id },
        }).catch(() => {
          stopLoginPolling();
          setPageViewType(LoginPageViewTypes.login);
        });
      }
    });
  };

  const loginViaMobileId = ({ phone, pin }: FieldValues) => {
    return loginByMobileId({
      phone,
      pin,
    }).then(async (result) => {
      if (result.data?.challenge?.session_id) {
        setPageViewType(LoginPageViewTypes.pinConfirmation);
        await delay(3000);
        getMobileIdState({
          variables: { session_id: result.data.challenge.session_id },
        }).catch(() => {
          setPageViewType(LoginPageViewTypes.login);
          stopLoginPolling();
        });
      }
    });
  };

  const loginViaPassword = ({ username, password }: FieldValues) => {
    return loginByPassword({
      username,
      password,
    }).then(async ({ data }) => {
      if (data?.success) {
        await executeSuccessfulLoginCallbacks();
      }
    });
  };

  const loginViaIdCard = () => {
    setPageViewType(LoginPageViewTypes.idCard);

    return loginByIdCard().then((response) => {
      if (response?.ok) {
        executeSuccessfulLoginCallbacks();
      } else {
        returnToLoginView();
      }
    });
  };

  const loginViaBanklink = ({ pin, payment_method_key }: FieldValues) => {
    return loginByBankLink({
      pin,
      payment_method_key,
      accept_url: banklinkLoginAcceptUrl,
      cancel_url: banklinkLoginCancelUrl,
      magic_login_url: banklinkLoginRedirectUrl,
      application_reference: applicationReferenceKey,
    }).then(({ data }) => {
      const redirectUrl = data?.challenge?.redirect_url;
      const sessionId = data?.challenge?.session_id;

      storeQueryParamsIfNeeded();

      if (sessionId) {
        setToStorage(LocalStorageKeys.sessionId, sessionId);
      }

      if (redirectUrl) {
        window.location.href = redirectUrl;

        return new Promise(() => {});
      }
    });
  };

  const loginViaEParaksts = (method: EparakstsAuthorizationMethod) => {
    storeEParakstsOriginalUri();

    return executeEParakstsLoginChallenge(method).then(({ data }) => {
      const redirectUrl = data?.challenge?.redirect_url;
      if (redirectUrl) {
        window.location.href = redirectUrl;

        return new Promise(() => {});
      }
    });
  };

  const onLoginFormSubmit = (formFieldValues: FieldValues) => {
    switch (selectedLoginMethod) {
      case AppLoginMethods.smartId:
        return loginViaSmartId(formFieldValues);
      case AppLoginMethods.mobileId:
        return loginViaMobileId(formFieldValues);
      case AppLoginMethods.idCard:
        return loginViaIdCard();
      case AppLoginMethods.password:
        return loginViaPassword(formFieldValues);
      case AppLoginMethods.banklink:
        return loginViaBanklink(formFieldValues);
      case AppLoginMethods.eParakstsMobile:
        return loginViaEParaksts(EparakstsAuthorizationMethod.MOBILE);
      case AppLoginMethods.eParakstsSmartCard:
        return loginViaEParaksts(EparakstsAuthorizationMethod.SMARTCARD);
      default:
        return;
    }
  };

  useEffectOnce(() => {
    if (storedQueryParams) {
      const params = Object.fromEntries(Array.from(searchParams));
      navigate(
        {
          search: createSearchParams({
            ...params,
            ...storedQueryParams,
          }).toString(),
        },
        { replace: true },
      );
      storeQueryParams(null);
    }
  });

  useEffectOnce(() => {
    if (magicLinkToken) {
      loginByMagicLink(magicLinkToken).then(({ data }) => {
        if (data?.success) {
          executeSuccessfulLoginCallbacks();
        }
      });
    }
  });

  useEffect(() => {
    if (eParakstsCode) {
      loginByEParaksts().then(({ data }) => {
        if (data?.success?.is_authenticated) {
          executeSuccessfulLoginCallbacks();
        }
      });
    }
  }, [eParakstsCode]);

  useEffectOnce(() => {
    if (payseraLoginPaymentStatus === PAYSERA_PAYMENT_STATUSES.successful) {
      setPageViewType(LoginPageViewTypes.pending);
      startPayseraLoginStatusPolling().catch(() => {
        returnToLoginView();
      });
    } else if (payseraLoginPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed) {
      toast.error(t(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  });

  useEffect(() => {
    switch (payseraLoginStatus) {
      case LoginPollStatus.SUCCESSFUL:
        stopPayseraLoginStatusPolling();
        removeFromStorage(LocalStorageKeys.sessionId);
        executeSuccessfulLoginCallbacks();
        break;
      case LoginPollStatus.FAILED:
        stopPayseraLoginStatusPolling();
        removeFromStorage(LocalStorageKeys.sessionId);
        setPageViewType(LoginPageViewTypes.login);
        break;
      case LoginPollStatus.EMAIL_REQUEST:
        setPageViewType(LoginPageViewTypes.magic);
        break;
      case LoginPollStatus.MAGIC_LINK_SENT:
        setPageViewType(LoginPageViewTypes.pending);
        break;
      default:
        break;
    }
  }, [payseraLoginStatus]);

  //TODO: move back once we have resolved on the backend (now it's hardcoded Estonian)
  // useEffect(() => {
  // 	if (
  // 		visiblePageAttributes[PageAttributeNames.banklink] &&
  // 		!banklinks.length
  // 	) {
  // 		getBanklinks(ABBREVIATIONS_BY_LANGUAGES_MAP[i18n.language]);
  // 	}
  // }, [pageAttributes?.length]);

  useEffect(() => {
    if (authorizedBySmartId) {
      stopSmartIdStatePolling();
      executeSuccessfulLoginCallbacks();
    }
  }, [authorizedBySmartId]);

  useEffect(() => {
    if (authorizedByMobileId) {
      stopMobileIdStatePolling();
      executeSuccessfulLoginCallbacks();
    }
  }, [authorizedByMobileId]);

  useEffect(() => {
    if (smartIdPollError) {
      setPageViewType(LoginPageViewTypes.login);
      stopSmartIdStatePolling();
    }
  }, [smartIdPollError]);

  useEffect(() => {
    if (mobileIdPollError) {
      setPageViewType(LoginPageViewTypes.login);
      stopMobileIdStatePolling();
    }
  }, [mobileIdPollError]);

  return {
    processingLoginPage:
      loginProcessingMap[selectedLoginMethod] ||
      eParakstsLoginProcessing ||
      loginByMagicLinkProcessing ||
      successfulLoginCallbacksProcessing,
    smartIdChallengeId: smartIdChallengeData?.challenge_id || '',
    mobileIdChallengeId: mobileIdChallengeData?.challenge_id || '',
    selectedLoginMethod,
    banklinkOptions: banklinks,
    loginButtons: loginMethodButtonsConfig,
    loginButtonDisabled:
      loginProcessingMap[selectedLoginMethod] ||
      loginByMagicLinkProcessing ||
      eParakstsLoginProcessing,
    visiblePageAttributes,
    onLoginFormSubmit,
    checkBanklinkButtonDisabled,
    loginValidationErrors,
    pageViewType,
    setPageViewType,
    isLoginFlowWithError: loginFLowErrorsByMethodMap[selectedLoginMethod],
    stopLoginPolling,
    onIdCardLoginCancel: returnToLoginView,
    loginByIdCardProcessing,
  };
};
