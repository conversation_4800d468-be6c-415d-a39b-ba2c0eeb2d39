import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import {
  ContractType,
  InsuranceStatus,
  UsersignInMethod,
} from 'api/core/generated';
import {
  ABBREVIATIONS_BY_LANGUAGES_MAP,
  AppSearchParams,
  BANKLINK_PAYMENT_POLL_STATUSES,
  FormFieldNames,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  PAYSERA_PAYMENT_STATUSES,
} from 'app-constants';
import { SigningPageViewTypes } from 'app-constants/insurance.constants';
import { useIncomeInsuranceRootContext } from 'context/income-insurance';
import { useRootContext } from 'context/root';
import { onlyPasswordSigningEnabled } from 'environment';
import { rootInsuranceStore } from 'hooks/root-logic/income-insurance/use-root-logic';
import { useGetBanklinkPaymentStatus } from 'hooks/use-banklink-payment-status-poll';
import { useBanklinkSigningPoll } from 'hooks/use-banklink-signing-poll';
import { useGetBanks } from 'hooks/use-get-banklinks';
import { useGetInsuranceByHashLazy } from 'hooks/use-get-insurance-by-hash-lazy';
import { useSignContractWithBanklink } from 'hooks/use-sign-contract-with-banklink';
import { useSignContractWithIdCard } from 'hooks/use-sign-contract-with-id-card';
import { useSignContractWithMobileId } from 'hooks/use-sign-contract-with-mobile-id';
import { useSignContractWithPassword } from 'hooks/use-sign-contract-with-password';
import { useSignContractWithSmartId } from 'hooks/use-sign-contract-with-smart-id';
import { useUpdateInsurance } from 'hooks/use-update-insurance';
import { useUpdateInsuranceTerms } from 'hooks/use-update-insurance-terms';
import { useUpdateUser } from 'hooks/use-update-user';
import type { IncomeInsuranceSigningPageLogic } from 'models';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  createSearchParams,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { useEffectOnce, useLocalStorage } from 'react-use';
import {
  extractValidationErrors,
  getFilteredUrlSearchParamsObject,
  loadAndInitializeIdCardScripts,
  removeFromStorage,
  setToStorage,
} from 'services';
import { toast } from 'sonner';
import * as z from 'zod';

const SigningPageFormSchema = z
  .object({
    [FormFieldNames.insuranceOption]: z.number(),
    [FormFieldNames.insuranceTermsOfService]: z.boolean(),
    [FormFieldNames.insuranceDataPolicy]: z.boolean(),
    [FormFieldNames.insuranceHealthAndEmployment]: z.boolean(),
    [FormFieldNames.phone]: z.string().optional(),
    [FormFieldNames.email]: z.string().optional(),
  })
  .refine(
    ({
      insurance_terms_of_service,
      insurance_data_policy,
      insurance_health_and_employment,
    }) =>
      insurance_terms_of_service &&
      insurance_data_policy &&
      insurance_health_and_employment,
    ({
      insurance_terms_of_service,
      insurance_data_policy,
      insurance_health_and_employment,
    }) => {
      if (!insurance_terms_of_service) {
        return {
          message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
          path: [FormFieldNames.insuranceTermsOfService],
        };
      }
      if (!insurance_data_policy) {
        return {
          message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
          path: [FormFieldNames.insuranceDataPolicy],
        };
      }
      if (!insurance_health_and_employment) {
        return {
          message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
          path: [FormFieldNames.insuranceHealthAndEmployment],
        };
      }
      return { message: '', path: [] };
    },
  );

export type SigningPageFormType = z.infer<typeof SigningPageFormSchema>;

declare let iSignApplet: {
  init: (options: Record<string, unknown>) => void;
  getCertificate: (options: Record<string, unknown>) => void;
  setHashAlgorithm: (algorithm: string) => void;
  sign: (
    dtbs: string,
    dtbsHash: string,
    callback: (signedHash: string) => void,
  ) => void;
};

export const useSigningPageLogic = (): IncomeInsuranceSigningPageLogic => {
  const { t: te, i18n } = useTranslation(LocizeNamespaces.errors);
  const { user, getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const { insuranceProviderOptions, storedInsurance } =
    useIncomeInsuranceRootContext();
  const { updateUser, userUpdateError } = useUpdateUser();
  const { updateInsurance } = useUpdateInsurance();
  const { updateInsuranceTerms } = useUpdateInsuranceTerms();
  const { getInsurance } = useGetInsuranceByHashLazy({
    fetchPolicy: 'network-only',
  });
  const [storedQueryParams, storeQueryParams] = useLocalStorage<Record<
    string,
    string
  > | null>(LocalStorageKeys.storedQueryParams, null);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const payseraSigningPaymentStatus = searchParams.get(
    AppSearchParams.payseraSigningPaymentStatus,
  );
  const [signingPageViewType, setSigningPageViewType] = useState(
    SigningPageViewTypes.signing,
  );
  const [
    signContractWithIDCardProcessing,
    setSignContractWithIDCardProcessing,
  ] = useState(false);

  const { getBanklinks, banklinks } = useGetBanks();

  const { signContractWithPassword, signContractWithPasswordProcessing } =
    useSignContractWithPassword();
  const {
    signContractWithSmartId,
    prepareSmartIdContractSignature,
    smartIdContractSignaturePreparationChallenge,
    smartIdContractSignaturePreparationLoading,
    smartIdContractSignaturePreparationError,
  } = useSignContractWithSmartId();
  const { signContractWithIdCard, prepareIdCardContractSignature } =
    useSignContractWithIdCard();
  const {
    signContractWithMobileId,
    prepareMobileIdContractSignature,
    mobileIdContractSignaturePreparationChallenge,
    mobileIdContractSignaturePreparationLoading,
    mobileIdContractSignaturePreparationError,
  } = useSignContractWithMobileId();
  const {
    prepareBanklinkContractSignature,
    banklinkSigningProcessing,
    banklinkSigningAcceptUrl,
    banklinkSigningCancelUrl,
  } = useSignContractWithBanklink();
  const {
    banklinkPaymentStatus,
    startBanklinkPaymentStatusPolling,
    stopBanklinkPaymentStatusPolling,
  } = useGetBanklinkPaymentStatus();
  const {
    isBanklinkSigningPollSuccess,
    startBanklinkSigningStatusPolling,
    stopBanklinkSigningStatusPolling,
  } = useBanklinkSigningPoll();

  const signInMethod = user?.sign_in_method ?? null;

  const userCanSignContract = signInMethod !== UsersignInMethod.PASSWORD;

  const shouldShowEmailPhoneFields = !user?.email || !user.phone;

  const userUpdateErrors = extractValidationErrors(userUpdateError);

  const form = useForm<SigningPageFormType>({
    resolver: zodResolver(SigningPageFormSchema),
    defaultValues: {
      [FormFieldNames.insuranceOption]:
        Math.round(Number(insuranceProviderOptions?.length) / 2) - 1,
      [FormFieldNames.insuranceTermsOfService]: false,
      [FormFieldNames.insuranceDataPolicy]: false,
      [FormFieldNames.insuranceHealthAndEmployment]: false,
      [FormFieldNames.phone]: user?.phone ?? undefined,
      [FormFieldNames.email]: user?.email ?? undefined,
    },
  });

  const checkInsuranceRejectedStatus = async () => {
    const { data } = await getInsurance();
    const insurance = data?.insurance;
    if (insurance?.status === InsuranceStatus.REJECTED) {
      rootInsuranceStore.storedInsuranceGlobal = insurance;
      getPageUrlAndNavigate(true);
    } else {
      setSigningPageViewType(SigningPageViewTypes.signing);
    }
  };

  const executeSuccessfulSigningCallbacks = () => {
    getPageUrlAndNavigate(true);
  };

  const signAppWithPassword = () => {
    signContractWithPassword({
      insurance_id: storedInsurance?.id,
      contract_type: ContractType.INCOME_INSURANCE_SIGNED,
    })
      .then(() => {
        executeSuccessfulSigningCallbacks();
      })
      .catch(async () => {
        await checkInsuranceRejectedStatus();
      });
  };

  const prepareSigningAppWithSmartId = () => {
    prepareSmartIdContractSignature({
      insurance_id: storedInsurance?.id,
      contract_type: ContractType.INCOME_INSURANCE_SIGNED,
    })
      .then(() => {
        setSigningPageViewType(SigningPageViewTypes.pinConfirmation);
      })
      .catch(async () => {
        await checkInsuranceRejectedStatus();
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
  };

  const signAppWithIdCard = () => {
    const lang = user?.language_abbr.split('-')[0];

    iSignApplet.init({
      certificatePurpose: 'sign',
      codebase: `${window.location.protocol}//${window.location.host}/dokobit`,
      language: lang,
      supportedResidencies: ['ee'],
    });

    setSignContractWithIDCardProcessing(true);

    (
      window as unknown as Window & {
        certificateSelected: (certificate: string) => void;
      }
    ).certificateSelected = (certificate: string) => {
      const cert = btoa(unescape(encodeURIComponent(certificate)));
      prepareIdCardContractSignature({
        insurance_id: storedInsurance?.id,
        certificate: cert,
        contract_type: ContractType.INCOME_INSURANCE_SIGNED,
      })
        .then(({ data: prepareSignatureData }) => {
          iSignApplet.setHashAlgorithm(
            prepareSignatureData?.challenge?.algorithm || '',
          );

          iSignApplet.sign(
            prepareSignatureData?.challenge?.dtbs || '',
            prepareSignatureData?.challenge?.dtbs_hash || '',
            (signedHash: string) => {
              signContractWithIdCard({
                token: prepareSignatureData?.challenge?.token || '',
                signature: signedHash,
              })
                .then(({ data }) => {
                  if (data?.success) {
                    executeSuccessfulSigningCallbacks();
                  } else {
                    setSigningPageViewType(SigningPageViewTypes.signing);
                    toast.error(
                      te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError),
                    );
                  }
                })
                .catch(async () => {
                  await checkInsuranceRejectedStatus();
                  setSignContractWithIDCardProcessing(false);
                });
            },
          );
        })
        .catch(async () => {
          await checkInsuranceRejectedStatus();
          setSignContractWithIDCardProcessing(false);
          toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        });
    };
  };

  const prepareSigningAppWithMobileId = () => {
    prepareMobileIdContractSignature({
      insurance_id: storedInsurance?.id,
      contract_type: ContractType.INCOME_INSURANCE_SIGNED,
    })
      .then(() => {
        setSigningPageViewType(SigningPageViewTypes.pinConfirmation);
      })
      .catch(async () => {
        await checkInsuranceRejectedStatus();
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
  };

  const signAppWithMobileId = () =>
    signContractWithMobileId()
      .then(({ data }) => {
        if (data?.success) {
          executeSuccessfulSigningCallbacks();
        } else {
          setSigningPageViewType(SigningPageViewTypes.signing);
          toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        }
      })
      .catch(async () => {
        await checkInsuranceRejectedStatus();
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
      });

  const signAppWithSmartId = () =>
    signContractWithSmartId()
      .then(({ data }) => {
        if (data?.success) {
          executeSuccessfulSigningCallbacks();
        } else {
          setSigningPageViewType(SigningPageViewTypes.signing);
          toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        }
      })
      .catch(async () => {
        await checkInsuranceRejectedStatus();
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
      });

  const signAppByMobileIdOrSmartId = () => {
    if (signInMethod === UsersignInMethod.MOBILE) {
      return signAppWithMobileId();
    }
    // default is smart id
    return signAppWithSmartId();
  };

  const signAppWithBanklink = (payment_method_key: string) => {
    prepareBanklinkContractSignature({
      insurance_id: storedInsurance?.id,
      contract_type: ContractType.INCOME_INSURANCE_SIGNED,
      payment_method_key,
      accept_url: banklinkSigningAcceptUrl,
      cancel_url: banklinkSigningCancelUrl,
    })
      .then(({ data }) => {
        const redirectUrl = data?.challenge?.redirect_url;
        const sessionId = data?.challenge?.session_id;

        const queryParamsToStore = getFilteredUrlSearchParamsObject(
          {
            [AppSearchParams.creditAccountHash]: true,
          },
          searchParams,
        );

        if (Object.keys(queryParamsToStore).length) {
          storeQueryParams(queryParamsToStore);
        }

        if (sessionId) {
          setToStorage(LocalStorageKeys.sessionId, sessionId);
        }

        if (redirectUrl) {
          window.location.href = redirectUrl;
        }
      })
      .catch(async () => {
        await checkInsuranceRejectedStatus();
      });
  };

  const signApp = (onBanklinkSigning: () => void) => {
    if (onlyPasswordSigningEnabled) {
      signAppWithPassword();
      return;
    }

    switch (signInMethod) {
      case UsersignInMethod.PAYSERA_BANKLINK:
      case UsersignInMethod.MAGIC_LINK:
        onBanklinkSigning();
        break;
      case UsersignInMethod.MOBILE:
        prepareSigningAppWithMobileId();
        break;
      case UsersignInMethod.SMART_ID:
        prepareSigningAppWithSmartId();
        break;
      case UsersignInMethod.ID_CARD:
        signAppWithIdCard();
        break;
      default:
        signAppWithPassword();
        break;
    }
  };

  const onFormSubmit =
    ({ onBanklinkSigning }: { onBanklinkSigning: () => void }) =>
    async (data: SigningPageFormType) => {
      setFormSubmitting(true);
      try {
        if (user?.id && (!user?.email || !user.phone)) {
          await updateUser({
            userId: user.id,
            email: data.email,
            phone: data.phone,
          });
        }

        const insuranceOptionToUpdate =
          insuranceProviderOptions?.[data.insurance_option];

        if (!storedInsurance) {
          throw new Error('No stored insurance found on update');
        }

        if (
          !insuranceOptionToUpdate ||
          !insuranceOptionToUpdate.provider_type
        ) {
          throw new Error('No full insurance option found on update');
        }

        await updateInsurance({
          insurance_id: storedInsurance.id,
          provider_type: insuranceOptionToUpdate.provider_type,
          insured_amount: insuranceOptionToUpdate.insured_amount,
          premium_amount: insuranceOptionToUpdate.premium_amount,
          partner_premium_amount:
            insuranceOptionToUpdate.partner_premium_amount,
          insurance_period: insuranceOptionToUpdate.insurance_period,
        });

        await updateInsuranceTerms({
          insurance_id: storedInsurance.id,
        });

        signApp(onBanklinkSigning);
        setFormSubmitting(false);
      } catch (error) {
        setFormSubmitting(false);
        console.error(error);
      }
    };

  const onPinConfirmationCancel = () => {
    setSigningPageViewType(SigningPageViewTypes.signing);
  };

  useEffectOnce(() => {
    if (storedQueryParams) {
      const params = Object.fromEntries(Array.from(searchParams));
      navigate(
        {
          search: createSearchParams({
            ...params,
            ...storedQueryParams,
          }).toString(),
        },
        { replace: true },
      );
      storeQueryParams(null);
    }
  });

  useEffect(() => {
    if (isBanklinkSigningPollSuccess) {
      stopBanklinkSigningStatusPolling();
      executeSuccessfulSigningCallbacks();
    }
  }, [isBanklinkSigningPollSuccess]);

  useEffect(() => {
    if (
      (signInMethod === UsersignInMethod.PAYSERA_BANKLINK ||
        signInMethod === UsersignInMethod.MAGIC_LINK) &&
      !banklinks.length
    )
      getBanklinks(ABBREVIATIONS_BY_LANGUAGES_MAP[i18n.language]);
  }, []);

  useEffectOnce(() => {
    if (payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.successful) {
      setSigningPageViewType(SigningPageViewTypes.pending);
      startBanklinkPaymentStatusPolling().catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
    } else if (
      payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed
    ) {
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  });

  useEffect(() => {
    switch (banklinkPaymentStatus) {
      case BANKLINK_PAYMENT_POLL_STATUSES.success:
        stopBanklinkPaymentStatusPolling();
        startBanklinkSigningStatusPolling()
          .then(({ data }) => {
            if (data?.success) {
              stopBanklinkSigningStatusPolling();
              removeFromStorage(LocalStorageKeys.sessionId);
              executeSuccessfulSigningCallbacks();
            }
          })
          .catch((error) => {
            // Stop polling only if the code is not 3803
            // 3803 means that the Payment was not confirmed by Paysera
            if (error.code !== 3803) {
              stopBanklinkPaymentStatusPolling();
            }
          });
        break;
      case BANKLINK_PAYMENT_POLL_STATUSES.failed:
        stopBanklinkPaymentStatusPolling();
        removeFromStorage(LocalStorageKeys.sessionId);
        setSigningPageViewType(SigningPageViewTypes.signing);
        break;
      default:
        break;
    }
  }, [banklinkPaymentStatus]);

  useEffect(() => {
    if (signInMethod === UsersignInMethod.ID_CARD) {
      loadAndInitializeIdCardScripts();
    }
  }, [signInMethod]);

  return {
    processingSigning:
      formSubmitting ||
      pageUrlAndNavigationProcessing ||
      signContractWithPasswordProcessing ||
      smartIdContractSignaturePreparationLoading ||
      signContractWithIDCardProcessing ||
      mobileIdContractSignaturePreparationLoading ||
      banklinkSigningProcessing,
    prepareContractSigning:
      smartIdContractSignaturePreparationLoading ||
      mobileIdContractSignaturePreparationLoading,
    smartIdContractSignaturePreparationLoading,
    mobileIdContractSignaturePreparationLoading,
    smartIdSigningChallengeViewIsVisible:
      Boolean(smartIdContractSignaturePreparationChallenge) &&
      !smartIdContractSignaturePreparationError,
    mobileIdSigningChallengeViewIsVisible:
      Boolean(mobileIdContractSignaturePreparationChallenge) &&
      !mobileIdContractSignaturePreparationError,
    smartIdContractSignaturePollChallengeId:
      smartIdContractSignaturePreparationChallenge?.challenge_id || '',
    mobileIdContractSignaturePollChallengeId:
      mobileIdContractSignaturePreparationChallenge?.challenge_id || '',
    form,
    signingPageViewType,
    onPinConfirmationCancel,
    onFormSubmit,
    insuranceProviderOptions,
    shouldShowEmailPhoneFields,
    userCanSignContract,
    signAppWithBanklink,
    signAppByMobileIdOrSmartId,
    banklinkOptions: banklinks,
    signInMethod,
    userUpdateErrors,
  };
};
