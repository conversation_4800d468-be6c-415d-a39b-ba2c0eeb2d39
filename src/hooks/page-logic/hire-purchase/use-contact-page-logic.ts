import { zodResolver } from '@hookform/resolvers/zod';
import { PoliticalExposure } from 'api/core/generated';
import {
  FormFieldNames,
  GoogleAnalyticsEvents,
  LOCIZE_USER_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { regionPhonePrefix } from 'environment';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { useGetPageAttributesSuspense } from 'hooks/use-get-page-attributes-suspense';
import { useGetPoliticalExposures } from 'hooks/use-get-political-exposures';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useUpdateUserInfo } from 'hooks/use-update-user-info';
import { useUpdateUserTerms } from 'hooks/use-update-user-terms';
import { useEffectOnce } from 'hooks/utils';
import type { Option } from 'models';
import { useEffect, useMemo } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  convertPageAttributeNamesToObject,
  extractValidationErrors,
  filterObjectByExistingKeysInObject,
  formatApplicationToContactPageDataFormat,
} from 'services';
import { processGqlFormValidationErrors } from 'utils/parseGraphQLError';
import * as z from 'zod';

export const ContactFormSchema = z.object({
  [FormFieldNames.name]: z.string(),
  [FormFieldNames.email]: z.string().email(),
  [FormFieldNames.phone]: z.string(),
  [FormFieldNames.address]: z.string(),
  [FormFieldNames.city]: z.string(),
  [FormFieldNames.postCode]: z.string(),
  [FormFieldNames.iban]: z.string(),
  [FormFieldNames.politicalExposure]: z.nativeEnum(PoliticalExposure),
  [FormFieldNames.conditionsAgreement]: z.boolean(),
  [FormFieldNames.conditionsAndPensionAgreement]: z.boolean(),
  [FormFieldNames.newsletterAgreement]: z.boolean(),
  [FormFieldNames.allowPensionQuery]: z.boolean(),
});

export type ContactFormType = z.infer<typeof ContactFormSchema>;

export const useContactPageLogic = () => {
  const { t } = useTranslation(LocizeNamespaces.user);

  const { application } = useGetCurrentApplicationSuspense();
  const { pageAttributes } = useGetPageAttributesSuspense();

  const { logAction } = useLogApplicationAction();
  const { getPageUrlAndNavigate, trackGoogleAnalyticsEvent } = useRootContext();
  const { getPoliticalExposures, politicalExposures } =
    useGetPoliticalExposures();
  const { updateUserInfo, userInfoError } = useUpdateUserInfo();
  const { updateUserTerms } = useUpdateUserTerms();

  const userInfoValidationErrors = extractValidationErrors(userInfoError);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const shouldShowPoliticalExposureSelect =
    visiblePageAttributes[PageAttributeNames.politicalExposureSelect];

  const {
    fullName,
    email,
    phone,
    address,
    city,
    postCode,
    iban,
    politicalExposure,
    applicationUserInfoId,
    userId,
    productId,
    newsletterAgreement,
    conditionsAgreement,
    phoneAreaCode,
    allowPensionQuery,
  } = useMemo(
    () => formatApplicationToContactPageDataFormat(application),
    [application],
  );

  const form = useForm<ContactFormType>({
    resolver: zodResolver(ContactFormSchema),
    defaultValues: {
      [FormFieldNames.name]: fullName ?? '',
      [FormFieldNames.email]: email ?? undefined,
      [FormFieldNames.phone]: phone ?? undefined,
      [FormFieldNames.address]: address ?? undefined,
      [FormFieldNames.city]: city ?? undefined,
      [FormFieldNames.postCode]: postCode ?? undefined,
      [FormFieldNames.iban]: iban ?? '',
      [FormFieldNames.politicalExposure]: t(
        `${LOCIZE_USER_TRANSLATION_KEYS.politicalExposurePrefix}${
          politicalExposure ?? PoliticalExposure.NONE
        }`,
      ) as PoliticalExposure,
      [FormFieldNames.conditionsAgreement]: conditionsAgreement ?? undefined,
      [FormFieldNames.conditionsAndPensionAgreement]:
        (conditionsAgreement && allowPensionQuery) ?? undefined,
      [FormFieldNames.newsletterAgreement]: newsletterAgreement ?? undefined,
      [FormFieldNames.allowPensionQuery]: allowPensionQuery ?? undefined,
    },
  });

  const getPoliticalExposureOptions = (): Array<Option> =>
    politicalExposures?.map((politicalExposure) => ({
      label: t(
        `${LOCIZE_USER_TRANSLATION_KEYS.politicalExposurePrefix}${politicalExposure}`,
      ),
      value: politicalExposure,
    })) ?? [];

  const onContactFormSubmit = async (
    {
      phone,
      address,
      post_code,
      city,
      email,
      political_exposure,
      conditions_agreement,
      allow_pension_query,
      newsletter_agreement,
      iban,
    }: ContactFormType,
    formMethods: UseFormReturn<ContactFormType>,
  ) => {
    if (!userId) {
      throw new Error('User ID is missing');
    }

    const conditionsAgreementValue =
      conditions_agreement ?? conditionsAgreement;
    const allowPensionQueryValue = allow_pension_query ?? allowPensionQuery;

    try {
      if (!userId) {
        throw new Error('User ID is missing');
      }

      const variablesFilteredByVisiblePageAttributes =
        filterObjectByExistingKeysInObject(
          {
            email,
            phone,
            address,
            post_code,
            city,
            iban,
          },
          formMethods.control._fields,
        );

      // Order of operations is important here
      await updateUserTerms({
        user_id: userId,
        newsletter_agreement: newsletter_agreement ?? newsletterAgreement,
        conditions_agreement: conditionsAgreementValue,
        allow_pension_query: allowPensionQueryValue ?? false,
        political_exposure,
      });
      await updateUserInfo({
        application_user_info_id: applicationUserInfoId ?? 0,
        ...variablesFilteredByVisiblePageAttributes,
      });

      await getPageUrlAndNavigate(true);

      trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.contactCompleted);
    } catch (error) {
      if ((error as Error)?.message === 'Unauthorized') {
        await getPageUrlAndNavigate(true);
      } else {
        processGqlFormValidationErrors({
          error,
          setFormError: formMethods.setError,
        });
      }
    }
  };

  useEffectOnce(() => {
    if (shouldShowPoliticalExposureSelect && !politicalExposures?.length) {
      getPoliticalExposures();
    }
  });

  useEffect(() => {
    // LOGGING ACTION
    if (productId) {
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.choosingContactInfo,
      });
    }
  }, [productId]);

  return useMemo(
    () => ({
      form,
      onContactFormSubmit,
      conditionsAgreement,
      politicalExposureOptions: getPoliticalExposureOptions(),
      visiblePageAttributes,
      userInfoValidationErrors,
      phonePrefix: phoneAreaCode ? `+${phoneAreaCode}` : regionPhonePrefix,
    }),
    [
      form,
      phoneAreaCode,
      conditionsAgreement,
      userInfoValidationErrors,
      visiblePageAttributes,
      getPoliticalExposureOptions,
      onContactFormSubmit,
    ],
  );
};
