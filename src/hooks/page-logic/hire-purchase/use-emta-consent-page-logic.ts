import {
  AppSearchParams,
  EMTA_STATUSES,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { useGetEmtaConsent } from 'hooks/use-get-emta-consent';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useStoreAccountScoringInvitation } from 'hooks/use-store-account-scoring-invitation';
import type { AnyObject } from 'models';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { replaceOrAddQueryParamsInUrl } from 'services';

export const useEmtaConsentPageLogic = () => {
  const { search, pathname } = useLocation();
  const [searchParams] = useSearchParams();
  const emtaStatus = searchParams.get(AppSearchParams.emtaStatus) ?? '';
  const [isRedirectingToAccountScoring, setIsRedirectingToAccountScoring] =
    useState(false);
  const [isRedirectingToEmta, setIsRedirectingToEmta] = useState(false);
  const navigate = useNavigate();

  const { application } = useGetCurrentApplicationSuspense();

  const { getPageUrlAndNavigate, user } = useRootContext();

  const { logAction } = useLogApplicationAction();
  const {
    getEmtaConsent,
    startEmtaConsentPolling,
    stopEmtaConsentPolling,
    emtaConsent,
  } = useGetEmtaConsent();

  const { accountScoringInvitationData, storeAccountScoringInvitation } =
    useStoreAccountScoringInvitation();

  const onEmtaRedirectButtonClick = () => {
    if (emtaConsent?.url) {
      setIsRedirectingToEmta(true);

      window.location.href = emtaConsent?.url;

      if (application?.id) {
        logAction({
          productId: application.id,
          action: PURCHASE_FLOW_LOG_ACTIONS.emtaConsentRedirect,
        });
      }

      setTimeout(() => {
        setIsRedirectingToEmta(false);
      }, 2000);
    }
  };

  const onAccountScoringRedirectButtonClick = () => {
    if (accountScoringInvitationData?.response_url) {
      setIsRedirectingToAccountScoring(true);

      window.location.href = accountScoringInvitationData.response_url;

      if (application?.id) {
        logAction({
          productId: application.id,
          action: PURCHASE_FLOW_LOG_ACTIONS.accountScoringRedirected,
        });
      }

      setTimeout(() => {
        setIsRedirectingToAccountScoring(false);
      }, 2000);
    }
  };

  const goBackToScoringMethods = () => {
    const newSearchParams = new URLSearchParams(search);
    newSearchParams.delete(AppSearchParams.emtaStatus);

    navigate({
      pathname,
      search: newSearchParams.toString(),
    });
  };

  useEffect(() => {
    if (!user) {
      return;
    }

    if (application?.id) {
      requestEmtaConsent({
        application_id: application.id,
      });
    }

    const email = user.email;
    const language = user.language_abbr;

    if (!email) {
      return;
    }

    storeAccountScoringInvitation({
      user_id: user.id,
      credit_account_id: null,
      user_mail: email,
      user_name: `${user.profile?.first_name ?? ''} ${
        user.profile?.last_name ?? ''
      }`,
      redirect_url: `${pathname}${search}`,
      language,
      application_id: application?.id,
    });
  }, [application?.id, user]);

  const requestEmtaConsent = (productVariables: AnyObject) => {
    const returnUrl = replaceOrAddQueryParamsInUrl({
      returnWithBase: true,
      url: `${pathname}${search}`,
      params: {
        [AppSearchParams.emtaStatus]: EMTA_STATUSES.loading,
      },
    });

    if (!user?.id) {
      throw new Error('User id is not defined');
    }

    getEmtaConsent({
      user_id: user.id,
      return_url: returnUrl,
      ...productVariables,
    });
  };

  useEffect(() => {
    if (emtaStatus === EMTA_STATUSES.loading) {
      const returnUrl = replaceOrAddQueryParamsInUrl({
        returnWithBase: true,
        url: `${pathname}${search}`,
        params: {
          [AppSearchParams.emtaStatus]: EMTA_STATUSES.loading,
        },
      });

      if (!user?.id) {
        throw new Error('User id is not defined');
      }

      startEmtaConsentPolling({
        user_id: user.id,
        return_url: returnUrl,
      });
    }
  }, [emtaStatus]);

  useEffect(() => {
    if (emtaConsent?.valid_until) {
      const customCurrentPageUrl = replaceOrAddQueryParamsInUrl({
        returnWithBase: true,
        url: `${pathname}${search}`,
        params: {
          [AppSearchParams.emtaStatus]: EMTA_STATUSES.finished,
        },
      });

      stopEmtaConsentPolling();

      getPageUrlAndNavigate(true, { customCurrentPageUrl });
    }
  }, [emtaConsent?.valid_until]);

  return {
    onEmtaRedirectButtonClick,
    onAccountScoringRedirectButtonClick,
    returnedFromEmtaService: emtaStatus === EMTA_STATUSES.loading,
    emtaConsentButtonDisabled: !emtaConsent?.url,
    emtaConsentUrl: emtaConsent?.url,
    emtaConsentPageLoaded: !!emtaConsent,
    accountScoringButtonDisabled: !accountScoringInvitationData?.response_url,
    isRedirectingToAccountScoring,
    isRedirectingToEmta,
    goBackToScoringMethods,
  };
};
