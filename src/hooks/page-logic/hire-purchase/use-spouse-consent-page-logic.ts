import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { ContractType } from 'api/core/generated';
import {
  ABBREVIATIONS_BY_LANGUAGES_MAP,
  AppRegions,
  AppSearchParams,
  AppSigningMethods,
  BANKLINK_PAYMENT_POLL_STATUSES,
  FormFieldNames,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
  PAYSERA_PAYMENT_STATUSES,
  SpouseConsentPageViewTypes,
} from 'app-constants';
import { region } from 'environment';
import { useGetBanklinkPaymentStatus } from 'hooks/use-banklink-payment-status-poll';
import { useBanklinkSigningPoll } from 'hooks/use-banklink-signing-poll';
import { useGetApplicationByReferenceSuspense } from 'hooks/use-get-application-by-reference-suspense';
import { useGetBanks } from 'hooks/use-get-banklinks';
import { useSignContractWithBanklink } from 'hooks/use-sign-contract-with-banklink';
import { useSignContractWithMobileId } from 'hooks/use-sign-contract-with-mobile-id';
import { useSignContractWithSmartId } from 'hooks/use-sign-contract-with-smart-id';
import { useUpdateSpouseUserInfo } from 'hooks/use-update-spouse-user-info';
import type { HirePurchaseSpouseConsentPageLogic } from 'models';
import { useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { type FieldValues, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  extractValidationErrors,
  formatApplicationToSpouseConsentPageDataFormat,
  getEmploymentDateOptions,
  removeFromStorage,
  setToStorage,
} from 'services';
import { toast } from 'sonner';
import * as z from 'zod';

export const SpouseConsentPageFormSchema = z
  .object({
    [FormFieldNames.spouseNetIncomeMonthly]: z.number().nullable(),
    [FormFieldNames.spouseExpenditureMonthly]: z.number().nullable(),
    [FormFieldNames.spouseMonthlyLivingExpenses]: z.number().nullable(),
    [FormFieldNames.spouseEmploymentDate]: z.string().nullable(),
    [FormFieldNames.spouseOverdueDebt]: z.number().nullable(),
    [FormFieldNames.spouseSigningMethod]: z.nativeEnum(AppSigningMethods),
    [FormFieldNames.paymentMethodKey]: z.string().optional(),
    [FormFieldNames.spousePhone]: z.string().optional(),
  })
  .refine(
    (data) => {
      if (!data.spouse_signing_method) {
        return false;
      }
      return true;
    },
    {
      path: [FormFieldNames.spouseSigningMethod],
    },
  )
  .refine(
    (data) => {
      if (data.spouse_signing_method === AppSigningMethods.mobileId) {
        return !!data.spouse_phone?.length;
      }
      return true;
    },
    {
      path: [FormFieldNames.spousePhone],
    },
  )
  .refine(
    (data) => {
      if (data.spouse_signing_method === AppSigningMethods.banklink) {
        return !!data.payment_method_key?.length;
      }
      return true;
    },
    {
      path: [FormFieldNames.paymentMethodKey],
    },
  );

export type SpouseConsentPageFormType = z.infer<
  typeof SpouseConsentPageFormSchema
>;

export const useSpouseConsentPageLogic =
  (): HirePurchaseSpouseConsentPageLogic => {
    const [searchParams] = useSearchParams();
    const payseraSigningPaymentStatus = searchParams.get(
      AppSearchParams.payseraSigningPaymentStatus,
    );

    const { t } = useTranslation(LocizeNamespaces.contactExtra);
    const { t: tl, i18n } = useTranslation(LocizeNamespaces.login);
    const { t: te } = useTranslation(LocizeNamespaces.errors);

    const isBanklinkSigningAllowed = region !== AppRegions.et;

    const { application } = useGetApplicationByReferenceSuspense();

    const {
      spouseEmploymentDate,
      spouseMonthlyLivingExpenses,
      spouseExpenditureMonthly,
      spouseNetIncomeMonthly,
      productIdVariables,
      applicationUserInfoId,
      spouseOverdueDebt,
    } = formatApplicationToSpouseConsentPageDataFormat(application);

    const { getBanklinks, banklinks } = useGetBanks();
    const { updateSpouseInfo, spouseInfoUpdateError } =
      useUpdateSpouseUserInfo();
    const {
      signContractWithSmartId,
      prepareSmartIdContractSignature,
      smartIdContractSignaturePreparationChallenge,
    } = useSignContractWithSmartId();
    const {
      signContractWithMobileId,
      prepareMobileIdContractSignature,
      mobileIdContractSignaturePreparationChallenge,
      mobileIdContractSignaturePreparationError,
    } = useSignContractWithMobileId();
    const {
      prepareBanklinkContractSignature,
      banklinkSigningError,
      banklinkSigningAcceptUrl,
      banklinkSigningCancelUrl,
    } = useSignContractWithBanklink();
    const {
      banklinkPaymentStatus,
      startBanklinkPaymentStatusPolling,
      stopBanklinkPaymentStatusPolling,
    } = useGetBanklinkPaymentStatus();
    const {
      isBanklinkSigningPollSuccess,
      startBanklinkSigningStatusPolling,
      stopBanklinkSigningStatusPolling,
    } = useBanklinkSigningPoll();

    const [spouseConsentPageViewType, setSpouseConsentPageViewType] = useState(
      SpouseConsentPageViewTypes.signing,
    );

    const spouseConsentContractType = ContractType.SPOUSE_CONSENT;

    const spouseConsentFormValidationErrors = extractValidationErrors(
      spouseInfoUpdateError ||
        mobileIdContractSignaturePreparationError ||
        banklinkSigningError,
    );

    const spouseConsentEmploymentDateOptions = getEmploymentDateOptions(t);

    const spouseConsentSigningMethodSelectOptions = useMemo(() => {
      const methods = [
        {
          value: AppSigningMethods.mobileId,
          label: tl(LOCIZE_LOGIN_TRANSLATION_KEYS.mobileIdMethodButtonLabel),
        },
        {
          value: AppSigningMethods.smartId,
          label: tl(LOCIZE_LOGIN_TRANSLATION_KEYS.smartIdMethodButtonLabel),
        },
      ];

      if (isBanklinkSigningAllowed) {
        methods.push({
          value: AppSigningMethods.banklink,
          label: tl(LOCIZE_LOGIN_TRANSLATION_KEYS.banklinkMethodButtonLabel),
        });
      }

      return methods;
    }, [tl, isBanklinkSigningAllowed]);

    const form = useForm<SpouseConsentPageFormType>({
      resolver: zodResolver(SpouseConsentPageFormSchema),
      defaultValues: {
        [FormFieldNames.spouseNetIncomeMonthly]: spouseNetIncomeMonthly ?? null,
        [FormFieldNames.spouseExpenditureMonthly]:
          spouseExpenditureMonthly ?? null,
        [FormFieldNames.spouseMonthlyLivingExpenses]:
          spouseMonthlyLivingExpenses ?? null,
        [FormFieldNames.spouseEmploymentDate]: spouseEmploymentDate,
        [FormFieldNames.spouseOverdueDebt]: spouseOverdueDebt ?? null,
        [FormFieldNames.spouseSigningMethod]: undefined,
        [FormFieldNames.spousePhone]: undefined,
        [FormFieldNames.paymentMethodKey]: undefined,
      },
    });

    const returnToSigningPage = () => {
      form.reset();
      setSpouseConsentPageViewType(SpouseConsentPageViewTypes.signing);
    };

    const spouseSigningMethod = form.watch(FormFieldNames.spouseSigningMethod);

    const userName = `${application?.user_info?.first_name} ${application?.user_info?.last_name}`;

    const signAppWithMobileId = () => {
      return signContractWithMobileId()
        .then(({ data }) => {
          if (data?.success) {
            executeSuccessfulSigningCallbacks();
          } else {
            toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
          }
        })
        .catch(() => {
          setSpouseConsentPageViewType(SpouseConsentPageViewTypes.signing);
        });
    };

    const signAppWithSmartId = () => {
      return signContractWithSmartId()
        .then(({ data }) => {
          if (data?.success) {
            executeSuccessfulSigningCallbacks();
          } else {
            toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
          }
        })
        .catch(() => {
          setSpouseConsentPageViewType(SpouseConsentPageViewTypes.signing);
        });
    };

    const signAppByMobileIdOrSmartId = () => {
      setSpouseConsentPageViewType(SpouseConsentPageViewTypes.signingContract);
      const signInMethod = spouseSigningMethod;

      if (signInMethod === AppSigningMethods.mobileId) {
        return signAppWithMobileId();
      }
      return signAppWithSmartId();
    };

    const executeSuccessfulSigningCallbacks = () => {
      setSpouseConsentPageViewType(SpouseConsentPageViewTypes.success);
    };

    const prepareSigningAppWithSmartId = async () => {
      await prepareSmartIdContractSignature({
        contract_type: spouseConsentContractType,
        ...productIdVariables,
      }).then(() => {
        setSpouseConsentPageViewType(
          SpouseConsentPageViewTypes.pinConfirmation,
        );
      });
    };

    const prepareSigningAppWithMobileId = async ({
      spouse_phone,
    }: FieldValues) => {
      await prepareMobileIdContractSignature({
        contract_type: spouseConsentContractType,
        phone: spouse_phone,
        ...productIdVariables,
      }).then(() => {
        setSpouseConsentPageViewType(
          SpouseConsentPageViewTypes.pinConfirmation,
        );
      });
    };

    const signAppWithBanklink = async ({ payment_method_key }: FieldValues) => {
      await prepareBanklinkContractSignature({
        contract_type: spouseConsentContractType,
        payment_method_key,
        accept_url: banklinkSigningAcceptUrl,
        cancel_url: banklinkSigningCancelUrl,
        ...productIdVariables,
      }).then(({ data }) => {
        const redirectUrl = data?.challenge?.redirect_url;
        const sessionId = data?.challenge?.session_id;

        if (sessionId) {
          setToStorage(LocalStorageKeys.sessionId, sessionId);
        }

        if (redirectUrl) {
          window.location.href = redirectUrl;
        }

        return new Promise(() => {});
      });
    };

    const onSpouseConsentFormSubmit = async (formFieldValues: FieldValues) => {
      const {
        spouse_expenditure_monthly,
        spouse_monthly_living_expenses,
        spouse_net_income_monthly,
        spouse_employment_date,
        spouse_overdue_debt,
      } = formFieldValues;

      setSpouseConsentPageViewType(SpouseConsentPageViewTypes.preparing);

      try {
        await updateSpouseInfo({
          application_user_info_id: applicationUserInfoId ?? 0,
          spouse_net_income_monthly: +spouse_net_income_monthly || null,
          spouse_expenditure_monthly:
            spouse_expenditure_monthly !== ''
              ? +spouse_expenditure_monthly
              : null,
          spouse_monthly_living_expenses:
            spouse_monthly_living_expenses !== ''
              ? +spouse_monthly_living_expenses
              : null,
          spouse_employment_date: spouse_employment_date || null,
          spouse_overdue_debt: +spouse_overdue_debt || 0,
          reject_when_necessary: false,
        });

        switch (form.getValues(FormFieldNames.spouseSigningMethod)) {
          case AppSigningMethods.banklink:
            await signAppWithBanklink(formFieldValues);
            break;
          case AppSigningMethods.mobileId:
            await prepareSigningAppWithMobileId(formFieldValues);
            break;
          default:
            await prepareSigningAppWithSmartId();
            break;
        }
      } catch (error) {
        console.error(error);
        setSpouseConsentPageViewType(SpouseConsentPageViewTypes.failure);
      }
    };

    useEffect(() => {
      if (isBanklinkSigningPollSuccess) {
        stopBanklinkSigningStatusPolling();
        executeSuccessfulSigningCallbacks();
      }
    }, [isBanklinkSigningPollSuccess]);

    useEffect(() => {
      if (
        isBanklinkSigningAllowed &&
        spouseSigningMethod === AppSigningMethods.banklink &&
        !banklinks.length
      ) {
        getBanklinks(ABBREVIATIONS_BY_LANGUAGES_MAP[i18n.language]);
      }
    }, [isBanklinkSigningAllowed, spouseSigningMethod]);

    useLayoutEffect(() => {
      if (payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.successful) {
        setSpouseConsentPageViewType(
          SpouseConsentPageViewTypes.signingContract,
        );
        startBanklinkPaymentStatusPolling().catch(() => {
          setSpouseConsentPageViewType(SpouseConsentPageViewTypes.signing);
        });
      } else if (
        payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed
      ) {
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        setSpouseConsentPageViewType(SpouseConsentPageViewTypes.failure);
      }
    }, []);

    useEffect(() => {
      switch (banklinkPaymentStatus) {
        case BANKLINK_PAYMENT_POLL_STATUSES.success:
          stopBanklinkPaymentStatusPolling();
          startBanklinkSigningStatusPolling()
            .then(({ data }) => {
              if (data?.success) {
                stopBanklinkSigningStatusPolling();
                removeFromStorage(LocalStorageKeys.sessionId);
                executeSuccessfulSigningCallbacks();
              }
            })
            .catch((error) => {
              // Stop polling only if the code is not 3803
              // 3803 means that the Payment was not confirmed by Paysera
              if (error.code !== 3803) {
                stopBanklinkPaymentStatusPolling();
              }
            });
          break;
        case BANKLINK_PAYMENT_POLL_STATUSES.failed:
          stopBanklinkPaymentStatusPolling();
          removeFromStorage(LocalStorageKeys.sessionId);
          break;
        default:
          break;
      }
    }, [banklinkPaymentStatus]);

    return {
      form,
      onPinConfirmationCancel: returnToSigningPage,
      onFormSubmit: onSpouseConsentFormSubmit,
      spouseConsentFormValidationErrors,
      userName,
      spouseConsentEmploymentDateOptions,
      spouseConsentSigningMethodSelectOptions,
      banklinkOptions: banklinks,
      spouseConsentPageViewType,
      signAppByMobileIdOrSmartId,
      smartIdSpouseSignaturePollChallengeId:
        smartIdContractSignaturePreparationChallenge?.challenge_id || '',
      mobileIdSpouseSignaturePollChallengeId:
        mobileIdContractSignaturePreparationChallenge?.challenge_id || '',
      onTryAgainButtonClick: returnToSigningPage,
      spouseSigningMethod,
    };
  };
