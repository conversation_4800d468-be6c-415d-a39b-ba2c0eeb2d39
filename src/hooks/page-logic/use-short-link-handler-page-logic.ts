import {
  type ApplicationByShortRefQuery,
  ApplicationScheduleType,
} from 'api/core/generated';
import {
  AppRouterParams,
  AppSearchParams,
  AppSigningMethods,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useGetApplicationByShortRef } from 'hooks/use-get-application-by-short-ref';
import { useLogout } from 'hooks/use-logout';
import { useEffect } from 'react';
import {
  createSearchParams,
  useParams,
  useSearchParams,
} from 'react-router-dom';
import { getAppRoutePathByApplicationScheduleType } from 'services';

export const useShortLinkHandlerPageLogic = () => {
  const params = useParams();
  const [searchParams] = useSearchParams();
  const { getUser, getPageUrlAndNavigate } = useRootContext();
  const { logOut } = useLogout();
  const shouldLogout = Number(searchParams.get(AppSearchParams.logout)) === 1;

  const {
    getApplicationByShortRef,
    applicationByShortRef,
    applicationByShortRefLoading,
  } = useGetApplicationByShortRef();

  const handleRedirect = async (data: ApplicationByShortRefQuery) => {
    const user = await getUser();
    const userInfo = user?.data?.me;
    const application = data?.application;

    const { appRoutePath, productRoutePaths } =
      getAppRoutePathByApplicationScheduleType(
        application?.schedule_type as ApplicationScheduleType,
      );

    const phone = application?.application_reference?.phone;
    const redirectSearchParam = searchParams.get(AppSearchParams.redirect);
    const shouldRedirectToContactPage =
      application?.schedule_type !== ApplicationScheduleType.SMALL_LOAN &&
      (!!userInfo ||
        application?.schedule_type === ApplicationScheduleType.ESTO_PAY);

    const targetPage = shouldRedirectToContactPage
      ? productRoutePaths?.CONTACT
      : productRoutePaths?.CHECKOUT;

    if (
      phone &&
      (!redirectSearchParam ||
        redirectSearchParam === AppSigningMethods.mobileId)
    ) {
      searchParams.set(AppSearchParams.phone, phone);
      searchParams.set(AppSearchParams.redirect, AppSigningMethods.mobileId);
    } else if (redirectSearchParam) {
      searchParams.set(AppSearchParams.redirect, redirectSearchParam);
    }

    const search = createSearchParams(searchParams).toString();

    const redirectUrl = `${window.origin}/${appRoutePath}/${targetPage}?${
      AppSearchParams.referenceKey
    }=${application?.reference_key}${search ? '&' : ''}${search}`;

    await getPageUrlAndNavigate(null, {
      customCurrentPageUrl: redirectUrl,
    });
  };

  const handleLogoutAndRedirect = (data: ApplicationByShortRefQuery) => {
    logOut().then(() => {
      getUser();
      handleRedirect(data);
      searchParams.delete(AppSearchParams.logout);
    });
  };

  useEffect(() => {
    getApplicationByShortRef({
      short_reference: params[AppRouterParams.shortReferenceCode] ?? '',
    }).then(({ data }) => {
      if (data) {
        if (shouldLogout) {
          handleLogoutAndRedirect(data);
        } else {
          handleRedirect(data);
        }
      }
    });
  }, []);

  return {
    applicationByShortRefLoading,
    applicationByShortRef,
  };
};
