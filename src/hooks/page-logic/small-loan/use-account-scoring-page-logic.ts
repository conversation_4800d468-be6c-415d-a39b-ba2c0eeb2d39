import { AccountStatementProviderType } from 'api/core/generated';
import {
  AccountScoringStatuses,
  AccountScoringViewType,
  AppRegions,
  AppSearchParams,
  EMTA_STATUSES,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { region } from 'environment';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { useGetEmtaConsent } from 'hooks/use-get-emta-consent';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { usePollAccountScoringInvitation } from 'hooks/use-poll-account-scoring-invitation';
import { usePollAccountScoringStatement } from 'hooks/use-poll-account-scoring-statement';
import { useStoreAccountScoringInvitation } from 'hooks/use-store-account-scoring-invitation';
import { useUploadAccountStatement } from 'hooks/use-upload-account-statement';
import { useEffectOnce } from 'hooks/utils';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  formatApplicationToAccountScoringPageDataFormat,
  getFromStorage,
  removeFromStorage,
  replaceOrAddQueryParamsInUrl,
  setToStorage,
} from 'services';

const isEmtaSectionVisible = region === AppRegions.et;

export const useAccountScoringPageLogic = () => {
  const { t } = useTranslation(LocizeNamespaces.errors);

  const { getPageUrlAndNavigate } = useRootContext();

  const { application } = useGetCurrentApplicationSuspense();

  const { email, userName, language, productId, userId, productIdVariables } =
    formatApplicationToAccountScoringPageDataFormat(application);

  const [accountScoringViewType, setAccountScoringViewType] =
    useState<AccountScoringViewType>(
      getFromStorage(LocalStorageKeys.accountScoringView) ??
        AccountScoringViewType.APPLICATION,
    );

  const [isRedirectingToAccountScoring, setIsRedirectingToAccountScoring] =
    useState(false);
  const [isRedirectingToEmta, setIsRedirectingToEmta] = useState(false);
  const navigate = useNavigate();

  const { pathname, search } = useLocation();

  const { logAction } = useLogApplicationAction();
  const { uploadAccountStatement, uploadAccountStatementProcessing } =
    useUploadAccountStatement();
  const { accountScoringInvitationData, storeAccountScoringInvitation } =
    useStoreAccountScoringInvitation();
  const { accountScoringInvitation, startAccountScoringInvitationPolling } =
    usePollAccountScoringInvitation();
  const { accountScoringStatement, startAccountScoringStatementPolling } =
    usePollAccountScoringStatement();

  const { getEmtaConsent, emtaConsent } = useGetEmtaConsent();

  const finishAccountScoring = () => {
    // LOG ACTION
    if (productId) {
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.accountScoringProcessFinished,
      });
    }

    removeFromStorage(LocalStorageKeys.accountScoringView);
    getPageUrlAndNavigate(true);
  };

  const handleAccountScoringStatuses = (status: string) => {
    switch (status) {
      case AccountScoringStatuses.outdated:
        setAccountScoringViewType(AccountScoringViewType.APPLICATION);
        toast.error(t(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        break;
      case AccountScoringStatuses.completed:
        finishAccountScoring();
        break;
      default:
        break;
    }
  };

  const onAccountScoringRedirectButtonClick = () => {
    if (accountScoringInvitationData?.response_url) {
      setIsRedirectingToAccountScoring(true);

      window.location.href = accountScoringInvitationData.response_url;

      if (productId) {
        logAction({
          productId,
          action: PURCHASE_FLOW_LOG_ACTIONS.accountScoringRedirected,
        });
      }

      setTimeout(() => {
        setIsRedirectingToAccountScoring(false);
      }, 2000);
    }
  };

  const onEmtaRedirectButtonClick = () => {
    if (emtaConsent?.url) {
      setIsRedirectingToEmta(true);

      window.location.href = emtaConsent.url;

      if (productId) {
        logAction({
          productId,
          action: PURCHASE_FLOW_LOG_ACTIONS.emtaConsentRedirect,
        });
      }

      setTimeout(() => {
        setIsRedirectingToEmta(false);
      }, 2000);
    }
  };

  const goBackToScoringMethods = () => {
    const newSearchParams = new URLSearchParams(search);
    newSearchParams.delete('accountScoringStatus');

    navigate({
      pathname,
      search: newSearchParams.toString(),
    });

    setAccountScoringViewType(AccountScoringViewType.APPLICATION);
  };

  const onAccountStatementUploaded = (
    statement: File,
    callback?: () => void,
  ) => {
    if (!userId) {
      throw new Error('User ID is missing');
    }

    uploadAccountStatement({
      user_id: userId,
      provider: AccountStatementProviderType.ACCOUNTSCORING,
      send_email: false,
      statement,
      ...productIdVariables,
    }).then(() => {
      callback?.();
      setAccountScoringViewType(AccountScoringViewType.MANUAL_UPLOAD_SCORING);
    });
  };

  const requestEmtaConsent = () => {
    if (!userId) {
      throw new Error('User id is not defined');
    }

    const returnUrl = replaceOrAddQueryParamsInUrl({
      returnWithBase: true,
      url: `${pathname}${search}`,
      params: {
        [AppSearchParams.emtaStatus]: EMTA_STATUSES.loading,
      },
    });

    getEmtaConsent({
      user_id: userId,
      return_url: returnUrl,
      ...productIdVariables,
    });
  };

  useEffectOnce(() => {
    if (isEmtaSectionVisible) {
      requestEmtaConsent();
    }
  });

  useEffect(() => {
    if (productId && userId) {
      // LOG ACTION
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.startingAccountScoring,
      });

      if (!email) {
        throw new Error('User email is missing');
      }
      if (!language) {
        throw new Error('Language is missing');
      }

      storeAccountScoringInvitation({
        user_id: userId,
        user_mail: email,
        user_name: userName,
        redirect_url: `${pathname}${search}`,
        language,
        ...productIdVariables,
      });

      startAccountScoringStatementPolling({
        user_id: userId,
        ...productIdVariables,
      });
    }
  }, [productId, userId]);

  useEffect(() => {
    if (accountScoringInvitationData) {
      if (!userId) {
        throw new Error('User ID is missing');
      }
      startAccountScoringInvitationPolling({
        response_id: Number(accountScoringInvitationData.response_id),
        user_id: userId,
        ...productIdVariables,
      });
    }
  }, [accountScoringInvitationData]);

  useEffect(() => {
    if (accountScoringInvitation) {
      handleAccountScoringStatuses(accountScoringInvitation.process_status);
    }
  }, [accountScoringInvitation]);

  useEffect(() => {
    if (accountScoringStatement) {
      handleAccountScoringStatuses(accountScoringStatement.process_status);
    }
  }, [accountScoringStatement]);

  useEffect(() => {
    if (
      accountScoringViewType === AccountScoringViewType.SCORING ||
      accountScoringViewType === AccountScoringViewType.MANUAL_UPLOAD_SCORING
    ) {
      setToStorage(LocalStorageKeys.accountScoringView, accountScoringViewType);
    }
  }, [accountScoringViewType]);

  return useMemo(
    () => ({
      finishAccountScoring,
      onAccountStatementUploaded,
      accountScoringViewType,
      setAccountScoringViewType,
      onAccountScoringRedirectButtonClick,
      isRedirectingToAccountScoring,
      onEmtaRedirectButtonClick,
      isRedirectingToEmta,
      uploadAccountStatementProcessing,
      goBackToScoringMethods,
      accountScoringUrl: accountScoringInvitationData?.response_url,
      accountScoringButtonDisabled: !accountScoringInvitationData?.response_url,
      isEmtaButtonDisabled: !emtaConsent?.url,
    }),
    [
      goBackToScoringMethods,
      uploadAccountStatementProcessing,
      finishAccountScoring,
      onAccountStatementUploaded,
      accountScoringViewType,
      onAccountScoringRedirectButtonClick,
      isRedirectingToAccountScoring,
      accountScoringInvitationData,
      onEmtaRedirectButtonClick,
      isRedirectingToEmta,
      emtaConsent,
    ],
  );
};
