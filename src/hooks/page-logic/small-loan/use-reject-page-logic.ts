import { REDIRECT_URLS } from 'app-constants';
import { useForwardLoan } from 'hooks/use-forward-loan';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { useGetPageAttributesSuspense } from 'hooks/use-get-page-attributes-suspense';
import type { SmallLoanRejectPageLogic } from 'models';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { convertPageAttributeNamesToObject } from 'services';
import { isAppLanguage } from 'utils/isAppLanguage';

export const useRejectPageLogic = (): SmallLoanRejectPageLogic => {
  const { i18n } = useTranslation();
  const { pageAttributes } = useGetPageAttributesSuspense();
  const { application } = useGetCurrentApplicationSuspense();
  const { getForwardLoanLink } = useForwardLoan();

  const [isForwardLoadLinkProcessing, setForwardLoadLinkProcessing] =
    useState(false);

  const [isRedirectingToCustomerProfile, setIsRedirectingToCustomerProfile] =
    useState(false);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const onReturnToMerchantButtonClick = (): void => {
    window.location.href = String(application?.cancel_url);
  };

  const onGoToFAQButtonClick = (): void => {
    if (isAppLanguage(i18n.language)) {
      window.location.href =
        REDIRECT_URLS.frequentlyAskedQuestionsPageUrls[i18n.language];
    } else {
      throw new Error('Unsupported language');
    }
  };

  const onEstoAccountButtonClick = (): void => {
    setIsRedirectingToCustomerProfile(true);
    window.location.href = REDIRECT_URLS.newCustomerProfile;
  };

  const onForwardLoanClick = () => {
    setForwardLoadLinkProcessing(true);

    getForwardLoanLink({ application_id: application.id })
      .then(({ data }) => {
        if (data?.url) {
          window.location.href = data.url;
        }
      })
      .catch(() => {
        setForwardLoadLinkProcessing(false);
      });
  };

  return useMemo(
    () => ({
      onReturnToMerchantButtonClick,
      onGoToFAQButtonClick,
      visiblePageAttributes,
      returnToMerchantButtonDisabled: !application?.cancel_url,
      onEstoAccountButtonClick,
      onForwardLoanClick,
      isRedirectingToCustomerProfile,
      isForwardLoadLinkProcessing,
    }),
    [
      onReturnToMerchantButtonClick,
      onGoToFAQButtonClick,
      visiblePageAttributes,
      application?.cancel_url,
      onEstoAccountButtonClick,
      onForwardLoanClick,
      isRedirectingToCustomerProfile,
      isForwardLoadLinkProcessing,
    ],
  );
};
