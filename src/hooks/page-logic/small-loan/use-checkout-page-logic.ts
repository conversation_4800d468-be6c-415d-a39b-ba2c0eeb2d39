import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import type { CreditSettingCalculator } from 'api/core/generated';
import {
  FormFieldNames,
  GoogleAnalyticsEvents,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useGetApplicationByReferenceSuspense } from 'hooks/use-get-application-by-reference-suspense';
import { useGetConsumerLoanCreditSettings } from 'hooks/use-get-consumer-loan-credit-settings';
import { useGetConsumerLoanScheduleSettingsSuspense } from 'hooks/use-get-consumer-loan-schedule-settings-suspense';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { useGetPageAttributesSuspense } from 'hooks/use-get-page-attributes-suspense';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useUpdateApplicationCampaign } from 'hooks/use-update-application-campaign-mutation';
import { useUpdateApplicationCreditInfo } from 'hooks/use-update-application-credit-info';
import { useDebounce, useEffectOnce } from 'hooks/utils';
import { useEffect, useMemo, useState } from 'react';
import { type FieldValues, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import {
  convertPageAttributeNamesToObject,
  getApplicationDefaultDownPayment,
  getDefaultPeriodMonths,
  getUpdateApplicationCreditInfoVariables,
  roundNumberToNearestTen,
  roundNumberToUpperTen,
  setSmallLoanAmountToStorage,
} from 'services';
import z from 'zod';

const CheckoutPageFormSchema = ({
  minAmount,
  maxAmount,
}: {
  minAmount: number;
  maxAmount: number;
}) =>
  z.object({
    [FormFieldNames.netTotal]: z.number().min(minAmount).max(maxAmount),
    [FormFieldNames.periodMonths]: z.string().min(1),
    [FormFieldNames.downPayment]: z.number().min(0),
  });

export type CheckoutPageFormType = z.infer<
  ReturnType<typeof CheckoutPageFormSchema>
>;

export const useCheckoutPageLogic = () => {
  const { t: te } = useTranslation(LocizeNamespaces.errors);

  const { logAction } = useLogApplicationAction();
  const {
    user,
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    trackGoogleAnalyticsEvent,
  } = useRootContext();
  const { pageAttributes } = useGetPageAttributesSuspense();
  const { application, applicationReferenceKey, quietApplicationRefetch } = (
    user
      ? useGetCurrentApplicationSuspense
      : useGetApplicationByReferenceSuspense
  )();

  const { minLoanAmount, maxLoanAmount, possiblePeriods } =
    useGetConsumerLoanScheduleSettingsSuspense({
      variables: {
        schedule_type: application.schedule_type,
      },
    });

  const { creditSettings, getCreditSettings, creditSettingsLoading } =
    useGetConsumerLoanCreditSettings();

  const { updateApplicationCreditInfo } = useUpdateApplicationCreditInfo();
  const { updateApplicationCampaign, applicationCampaignUpdating } =
    useUpdateApplicationCampaign();

  const [selectedPeriod, setSelectedPeriod] = useState<number>(0);
  const [isPaymentLeaveEnabled, setIsPaymentLeaveEnabled] = useState(false);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const formSchema = CheckoutPageFormSchema({
    minAmount: minLoanAmount,
    maxAmount: maxLoanAmount,
  });

  const form = useForm<CheckoutPageFormType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      [FormFieldNames.periodMonths]: (
        application.credit_info?.period_months ??
        possiblePeriods[possiblePeriods.length - 1] ??
        0
      ).toString(),
      [FormFieldNames.downPayment]:
        getApplicationDefaultDownPayment(application),
      [FormFieldNames.netTotal]: roundNumberToUpperTen(
        application.requested_amount,
      ),
    },
    mode: 'onChange',
  });

  const onLoanAmountChange = ({ net_total: netTotal }: FieldValues) => {
    const loanAmountIsValid =
      netTotal >= minLoanAmount && netTotal <= maxLoanAmount;

    if (loanAmountIsValid) {
      const { id: application_id } = application;

      form.clearErrors(FormFieldNames.netTotal);

      form.setValue(
        FormFieldNames.netTotal,
        roundNumberToNearestTen(netTotal),
        { shouldValidate: true },
      );

      const period = form.getValues(FormFieldNames.periodMonths);

      getCreditSettings({
        net_total: +netTotal,
        application_id: user ? application_id : undefined,
        schedule_type: application.schedule_type,
        period: +period,
        approximate: false,
        consider_max_monthly_payment: false,
      }).then((result) => {
        const settings = result?.data
          ?.settings as Array<CreditSettingCalculator>;

        const hasSelectedPeriodInSettings = settings?.find(
          ({ month }) => month === selectedPeriod,
        );

        const maxPeriod = Number(settings?.[settings.length - 1]?.month ?? 0);

        if (!hasSelectedPeriodInSettings) {
          setSelectedPeriod(maxPeriod);
          form.setValue(FormFieldNames.periodMonths, maxPeriod.toString());
        }
      });
    } else {
      form.setError(FormFieldNames.netTotal, {});
    }
  };

  const onPeriodChange = useDebounce(
    ({ period_months: periodMonths }: FieldValues) => {
      setSelectedPeriod(+periodMonths);

      const { id: application_id } = application;

      const netTotal = form.getValues(FormFieldNames.netTotal);

      getCreditSettings({
        net_total: +netTotal,
        application_id: user ? application_id : undefined,
        schedule_type: application.schedule_type,
        period: +periodMonths,
        approximate: true,
        consider_max_monthly_payment: false,
      });
    },
    300,
  );

  const onCheckoutPageFormSubmit = async (formFieldValues: FieldValues) => {
    const {
      period_months: periodMonths,
      down_payment: downPayment,
      net_total: netTotal,
    } = formFieldValues;

    try {
      if (Object.keys(form.formState.dirtyFields).length) {
        await updateApplicationCreditInfo(
          getUpdateApplicationCreditInfoVariables({
            application,
            applicationReferenceKey,
            downPayment,
            periodMonths: +periodMonths,
            netTotal,
          }),
        );

        await quietApplicationRefetch();
      }

      setSmallLoanAmountToStorage(+netTotal, application.id);

      trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.checkoutCompleted);

      await getPageUrlAndNavigate(true);
    } catch (error) {
      console.error(error);
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  };

  const onTogglePaymentLeaveOffer = async () => {
    try {
      await updateApplicationCampaign({
        applicationId: application.id,
        referenceKey: applicationReferenceKey,
        paymentLeaveEnabled: !isPaymentLeaveEnabled,
      });
      setIsPaymentLeaveEnabled(!isPaymentLeaveEnabled);
    } catch (error) {
      console.error(error);
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  };

  const checkoutPageFormConfig = {
    defaultValues: {
      [FormFieldNames.periodMonths]: getDefaultPeriodMonths(
        application.credit_info?.period_months ?? 0,
        creditSettings,
      ).toString(),
      [FormFieldNames.downPayment]:
        getApplicationDefaultDownPayment(application),
      [FormFieldNames.netTotal]: roundNumberToUpperTen(
        application.requested_amount,
      ),
    },
  };

  const nextButtonIsDisabled =
    creditSettingsLoading || applicationCampaignUpdating;

  useEffect(() => {
    form.trigger();
  }, []);

  useEffectOnce(() => {
    if (!application.id || !application.credit_info) return;

    getCreditSettings({
      net_total: application.credit_info.net_total,
      application_id: user ? application.id : undefined,
      schedule_type: application.schedule_type,
      period: application.credit_info.period_months,
      approximate: true,
      consider_max_monthly_payment: false,
    });
  });

  useEffect(() => {
    if (application.id) {
      const paymentLeaveEnabled = Boolean(
        application.campaign?.payment_leave_enabled,
      );

      setIsPaymentLeaveEnabled(paymentLeaveEnabled);
    }
  }, [application]);

  useEffect(() => {
    if (!selectedPeriod && application?.credit_info && creditSettings) {
      setSelectedPeriod(
        getDefaultPeriodMonths(
          application?.credit_info?.period_months,
          creditSettings,
        ),
      );
    }
  }, [application, creditSettings]);

  useEffect(() => {
    if (application.id) {
      logAction({
        productId: application.id,
        action: PURCHASE_FLOW_LOG_ACTIONS.choosingCreditSettings,
      });
    }
  }, [application.id]);

  return {
    application,
    onPeriodChange,
    onLoanAmountChange,
    minLoanAmount,
    maxLoanAmount,
    possiblePeriods,
    processingCheckout: pageUrlAndNavigationProcessing,
    checkoutPageFormConfig,
    onCheckoutPageFormSubmit,
    visiblePageAttributes,
    nextButtonIsDisabled,
    onTogglePaymentLeaveOffer,
    isPaymentLeaveEnabled,
    form,
    creditSettings,
    creditSettingsLoading,
  };
};
