import {
  CREDIT_LINE_PRICING_KEYS,
  PRICING_KEYS,
  PURCHASE_FLOW_LOG_ACTIONS,
  REDIRECT_URLS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useEffectOnce, useGetPageAttributesSuspense } from 'hooks';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { useGetPricing } from 'hooks/use-get-pricing';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import type { SmallLoanSuccessPageLogic } from 'models';
import { useEffect, useMemo } from 'react';
import {
  convertPageAttributeNamesToObject,
  formatPricingDataToObject,
} from 'services';

export const useSuccessPageLogic = (): SmallLoanSuccessPageLogic => {
  const { getUser, userLoading } = useRootContext();
  const { logAction } = useLogApplicationAction();
  const { pageAttributes } = useGetPageAttributesSuspense();
  const { application, applicationReferenceKey } =
    useGetCurrentApplicationSuspense();
  const { getPricingData, pricingData } = useGetPricing();

  const isWithDownPayment = Boolean(application?.credit_info?.down_payment);

  const pricingDataWithKeys = formatPricingDataToObject(pricingData);

  useEffect(() => {
    getPricingData({
      keys: [
        ...Object.values(CREDIT_LINE_PRICING_KEYS),
        PRICING_KEYS.everyPayProviderEnabled,
      ],
    });
  }, []);

  useEffectOnce(() => {
    if (application?.id) {
      logAction({
        productId: application.id,
        action: PURCHASE_FLOW_LOG_ACTIONS.successPage,
      });
    }
  });

  useEffectOnce(() => {
    getUser();
  });

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const onCustomerProfileRedirectionButtonClick = (): void => {
    const redirectUrl = isWithDownPayment
      ? `${REDIRECT_URLS.customerProfileApplication}/${applicationReferenceKey}?amount=${application?.credit_info?.down_payment}`
      : REDIRECT_URLS.customerProfileSubscription;

    window.location.href = redirectUrl;
  };

  const onEstoAccountButtonClick = (): void => {
    window.location.href = REDIRECT_URLS.newCustomerProfile;
  };

  return useMemo(
    () => ({
      onCustomerProfileRedirectionButtonClick,
      onEstoAccountButtonClick,
      visiblePageAttributes,
      isInstantPayout: application?.is_instant_payout,
      everyPayProviderEnabled:
        pricingDataWithKeys[PRICING_KEYS.everyPayProviderEnabled],
      application,
    }),
    [
      userLoading,
      onCustomerProfileRedirectionButtonClick,
      onEstoAccountButtonClick,
      visiblePageAttributes,
      application,
      pricingDataWithKeys,
    ],
  );
};
