import type { ApolloError } from '@apollo/client';
import {
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { idCardLoginEndpoint } from 'environment';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';

export const useLoginByIdCard = () => {
  const { t } = useTranslation(LocizeNamespaces.errors);

  const [loginByIdCardProcessing, setLoginByIdCardProcessing] = useState(false);
  const [loginByIdCardError, setLoginByIdCardError] = useState<
    ApolloError | boolean
  >(false);

  const url = `${idCardLoginEndpoint}/idlogin`;
  const headers = new Headers();
  headers.set(
    'Content-Type',
    'application/x-www-form-urlencoded; charset=UTF-8',
  );

  const loginByIdCard = () => {
    setLoginByIdCardProcessing(true);
    setLoginByIdCardError(false);

    return fetch(url, {
      method: 'GET',
      headers,
      credentials: 'include',
    })
      .catch((error) => {
        setLoginByIdCardError(error);

        toast.error(t(LOCIZE_ERRORS_TRANSLATION_KEYS.idCardLoginError));
      })
      .finally(() => {
        setLoginByIdCardProcessing(false);
      });
  };

  return {
    loginByIdCard,
    loginByIdCardProcessing,
    loginByIdCardError: loginByIdCardError,
  };
};
