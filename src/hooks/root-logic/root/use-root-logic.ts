import {
  APPLICATION_PRODUCT_TYPES,
  AppRoutePath<PERSON>,
  LocalStorageKeys,
  MOBILE_SCREEN_SIZE,
  TABLET_SCREEN_SIZE,
} from 'app-constants';
import { useGetApplicationByReferenceSuspense } from 'hooks/use-get-application-by-reference-suspense';
import type { RootLogic } from 'models';
import { useEffect } from 'react';
import { isNewDesignProduct } from 'utils/isNewDesignProduct';

import { useCheckUpdateClientUserLanguage } from '../../use-check-update-client-user-language';
import { useGetApplicationByReference } from '../../use-get-application-by-reference';
import { useGetApplicationPrivateInfo } from '../../use-get-application-private-info';
import { useGetCurrentStepCount } from '../../use-get-current-step-count';
import { useGetLanguages } from '../../use-get-languages';
import { useGetPageUrl } from '../../use-get-page-url';
import { useGetProductTypeByUrl } from '../../use-get-product-type-by-url';
import { useGetUser } from '../../use-get-user';
import { useGetUserSuspense } from '../../use-get-user-suspence';
import { useInitializeAppServices } from '../../use-initialize-app-services';
import { useTrackGoogleAnalytics } from '../../use-track-google-analytics';
import { useCheckMaxResolution, useLocalStorage } from '../../utils';

// TODO remove once we migrate HP and SL to new designs, should keep isNewDesignProduct till all flows are moved
const isShortLinkPath =
  window.location.pathname.split('/')[1] === AppRoutePaths.SHORT_LINK;

export const useRootLogic = (): RootLogic => {
  const productType = useGetProductTypeByUrl();
  const { languages } = useGetLanguages();
  const checkUpdateClientUserLanguage = useCheckUpdateClientUserLanguage();

  const isNewDesign = !isShortLinkPath && isNewDesignProduct(productType);

  const { user, getUser, userLoading, userWasFetched, quietUserRefetch } =
    isNewDesign ? useGetUserSuspense() : useGetUser();

  const { getApplication, application } = isNewDesign
    ? useGetApplicationByReferenceSuspense()
    : useGetApplicationByReference();
  const {
    applicationPrivateInfo,
    applicationPrivateInfoLoading,
    getApplicationPrivateInfo,
  } = useGetApplicationPrivateInfo(application.schedule_type);
  const currentStepCount = useGetCurrentStepCount();
  const isTablet = useCheckMaxResolution(TABLET_SCREEN_SIZE);
  const isMobile = useCheckMaxResolution(MOBILE_SCREEN_SIZE);
  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing, pageUrl } =
    useGetPageUrl();
  const trackGoogleAnalyticsEvent = useTrackGoogleAnalytics();
  const [initApplicationSchedule, setInitApplicationSchedule] = useLocalStorage<
    Record<number, string>
  >(LocalStorageKeys.initialApplicationSchedule, {});
  useInitializeAppServices(user);

  useEffect(() => {
    if (isNewDesign) return;

    if (
      !application.id &&
      productType &&
      APPLICATION_PRODUCT_TYPES.includes(productType)
    ) {
      getApplication();
    }
  }, [productType]);

  useEffect(() => {
    if (application.id && !initApplicationSchedule[application.id]) {
      setInitApplicationSchedule((prev) => ({
        ...prev,
        [application.id]: application.schedule_type,
      }));
    }
  }, [
    application.id,
    initApplicationSchedule,
    application.schedule_type,
    setInitApplicationSchedule,
  ]);

  useEffect(() => {
    if (userWasFetched) {
      checkUpdateClientUserLanguage();
    }
  }, [userWasFetched, checkUpdateClientUserLanguage]);

  return {
    pageUrl,
    productType,
    applicationPrivateInfo,
    applicationPrivateInfoLoading,
    application,
    languages,
    user,
    currentStepCount,
    isTablet,
    isMobile,
    getUser,
    quietUserRefetch,
    userWasFetched,
    userLoading,
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    getApplicationPrivateInfo,
    trackGoogleAnalyticsEvent,
  };
};
