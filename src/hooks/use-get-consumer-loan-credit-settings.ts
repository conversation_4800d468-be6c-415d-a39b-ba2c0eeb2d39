import {
  type CreditSettingCalculator,
  type SmallLoanCreditSettingsQuery,
  type SmallLoanCreditSettingsQueryVariables,
  useSmallLoanCreditSettingsLazyQuery,
} from 'api/core/generated';
import { AppApiVersions } from 'app-constants';
import { useEffect, useState } from 'react';

export const useGetConsumerLoanCreditSettings = () => {
  const [creditSettingsData, setCreditSettingsData] =
    useState<SmallLoanCreditSettingsQuery | null>(null);
  const [creditSettingsRequest, { data, loading, error }] =
    useSmallLoanCreditSettingsLazyQuery({
      context: { apiVersion: AppApiVersions.core },
      notifyOnNetworkStatusChange: true,
    });

  const getCreditSettings = (
    variables: SmallLoanCreditSettingsQueryVariables,
  ) => creditSettingsRequest({ variables });

  useEffect(() => {
    if (data && !loading) {
      setCreditSettingsData(data);
    }
  }, [data, loading]);

  return {
    getCreditSettings,
    creditSettings:
      creditSettingsData?.settings as Array<CreditSettingCalculator> | null,
    creditSettingsLoading: loading,
    creditSettingsError: error,
  };
};
