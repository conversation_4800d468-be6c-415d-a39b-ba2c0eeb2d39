export enum PageAttributeNames {
  downPayment = 'down_payment',
  periodSelector = 'period_selector',
  name = 'name',
  email = 'email',
  phoneNumber = 'phone_number',
  address = 'address',
  city = 'city',
  postalCode = 'postal_code',
  politicalExposureSelect = 'pep_check',
  termsAndConditionsCheckbox = 'terms_and_conditions_checkbox',
  pension_checkbox = 'terms_and_conditions_and_pension_checkbox',
  newsletterCheckbox = 'newsletter_checkbox',
  estoAccountSection = 'esto_account_section',
  returnToMerchantButton = 'return_to_merchant_button',
  standingOrderSection = 'standing_order_section',
  frequentlyAskedQuestionsSection = 'faq_section',
  iban = 'iban',
  banklink = 'banklink',
  smartId = 'smart_id',
  mobileId = 'mobile_id',
  idCard = 'id_card',
  password = 'password',
  expenditureMonthly = 'expenditure_monthly',
  monthlyLivingExpenses = 'monthly_living_expenses',
  netIncomeMonthly = 'net_income_monthly',
  numberOfDependents = 'number_of_dependents',
  eParakstsMobile = 'eparaksts_mobile',
  eParakstsSmartCard = 'eparaksts_smartcard',
  defaultPendingDisclaimer = 'default_pending_disclaimer',
  waitingSpouseConsentPendingDisclaimer = 'waiting_spouse_consent_pending_disclaimer',
  outsideWorkingHoursPendingDisclaimer = 'outside_working_hours_pending_disclaimer',
  spouseEmploymentDate = 'spouse_employment_date',
  employmentDate = 'employment_date',
  planningNewDebts = 'planning_new_debts',
  futureReducedEarnings = 'future_reduced_earnings',
  ultimateBeneficialOwner = 'ultimate_beneficial_owner',
  occupationCategoryDropdown = 'occupation_category',
  addLegalPersonToInvoice = 'add_legal_person_to_invoice',
  forwardRejectsSection = 'forward_rejects_section',
  spouseInstructionsSection = 'purpose_of_loan',
  checkIncomeButton = 'check_income_button',
  checkIncomeDisclaimer = 'check_income_disclaimer',
  checkoutPageAmountSlider = 'checkout_page_amount_slider',
  signingPageAmountSlider = 'signing_page_amount_slider',
  smartIdFullDisclaimer = 'smart_id_full_disclaimer',
  smallLoanGroupCampaignText = 'small_loan_group_campaign_text',
  radioButtonContainerStartPaymentAfterMonths = 'radio_button_container_start_payment_after_months',
  specialOfferContainerInterestFreeDisclaimer = 'interest_free_months',
  fixedZeroContractFeeDisclaimer = 'fixed_zero_contract_fee_disclaimer',
  fixedZeroManagementFeeDisclaimer = 'fixed_zero_management_fee_disclaimer',
  manualScoringNeededDisclaimer = 'manual_scoring_needed_disclaimer',
  overdueDebt = 'overdue_debt',
  overdueDisclaimer = 'overdue_disclaimer',
  unsignedCaWithLimitDisclaimer = 'unsigned_ca_with_limit_disclaimer',
  cawAvailableUpToDisclaimer = 'caw_available_up_to_disclaimer',
}
