import type { AppLoginMethods, LoginPageViewTypes } from 'app-constants';
import type { ButtonProps } from 'components/ui/button';
import type { Dispatch, SetStateAction } from 'react';
import type { FieldValues } from 'react-hook-form';

import type { AppButtonProps } from '../button.models';
import type { VisiblePageAttributes } from '../page-attributes.models';
import type { FieldValidationObject } from '../validation.models';

export type LoginPageLogic = {
  loginPageLoaded?: boolean;
  processingLoginPage: boolean;
  selectedLoginMethod: AppLoginMethods;
  smartIdChallengeId: string;
  mobileIdChallengeId: string;
  loginMethodButtons?: Array<AppButtonProps & { method: AppLoginMethods }>;
  loginButtons?: Array<
    ButtonProps & { method: AppLoginMethods; label: string }
  >;
  visiblePageAttributes: VisiblePageAttributes;
  loginValidationErrors: FieldValidationObject;
  onLoginFormSubmit: (formFieldValues: FieldValues) => void;
  checkBanklinkButtonDisabled: (method: AppLoginMethods) => boolean;

  banklinkOptions: Array<Record<string, string> | null>;
  pageViewType: LoginPageViewTypes;
  setPageViewType: Dispatch<SetStateAction<LoginPageViewTypes>>;
  loginButtonDisabled: boolean;
  isLoginFlowWithError: boolean;
  stopLoginPolling: () => void;
  backNavigationDirectionValue?: boolean | null;
  onIdCardLoginCancel?: () => void;
  loginByIdCardProcessing?: boolean;
};
