import {
  AppFormFieldTypes,
  AppLanguages,
  AppLoaderSizes,
  AppLoginMethods,
  AppRegions,
  FormFieldNames,
  FormFieldVariants,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import {
  AppForm,
  AppFormButton,
  AppFormField,
  AppLoader,
  PrivacyPolicyDisclaimer,
} from 'components';
import { useCreditLineWithdrawalLoginPageContext } from 'context/credit-line-withdrawal';
import { region, regionPhonePrefix } from 'environment';
import { useTranslation } from 'react-i18next';

import styles from './Login.module.scss';

const SmartIdLoginForm = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.login);

  const isLatvianRegionRussianSpecificCopy =
    region === AppRegions.lv && i18n.language === AppLanguages.ru;

  const { onLoginFormSubmit, loginValidationErrors, loginButtonDisabled } =
    useCreditLineWithdrawalLoginPageContext();

  return (
    <AppForm className={styles.form} onSubmit={onLoginFormSubmit}>
      <AppFormField
        invalid={loginValidationErrors[FormFieldNames.pin]}
        name={FormFieldNames.pin}
        placeholder={t(
          isLatvianRegionRussianSpecificCopy
            ? LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabelLV
            : LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabel,
        )}
        type={AppFormFieldTypes.text}
        variant={FormFieldVariants.login}
        visible
      />
      <AppFormButton
        className={styles['submit-button']}
        disablingEmptyFieldNames={[FormFieldNames.pin]}
        isDisabled={loginButtonDisabled}
        isSubmit
        label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
      />
      <PrivacyPolicyDisclaimer />
    </AppForm>
  );
};

const MobileIdLoginForm = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.login);

  const isLatvianRegionRussianSpecificCopy =
    region === AppRegions.lv && i18n.language === AppLanguages.ru;

  const { onLoginFormSubmit, loginValidationErrors, loginButtonDisabled } =
    useCreditLineWithdrawalLoginPageContext();

  return (
    <AppForm className={styles.form} onSubmit={onLoginFormSubmit}>
      <AppFormField
        invalid={loginValidationErrors[FormFieldNames.phone]}
        name={FormFieldNames.phone}
        placeholder={t(LOCIZE_LOGIN_TRANSLATION_KEYS.phoneFieldLabel)}
        prefix={regionPhonePrefix}
        type={AppFormFieldTypes.phone}
        variant={FormFieldVariants.login}
        visible
      />
      <AppFormField
        invalid={loginValidationErrors[FormFieldNames.pin]}
        name={FormFieldNames.pin}
        placeholder={t(
          isLatvianRegionRussianSpecificCopy
            ? LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabelLV
            : LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabel,
        )}
        type={AppFormFieldTypes.text}
        variant={FormFieldVariants.login}
        visible
      />
      <AppFormButton
        className={styles['submit-button']}
        disablingEmptyFieldNames={[FormFieldNames.pin]}
        isDisabled={loginButtonDisabled}
        isSubmit
        label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
      />
      <PrivacyPolicyDisclaimer />
    </AppForm>
  );
};

const IdCardLoginForm = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { onLoginFormSubmit } = useCreditLineWithdrawalLoginPageContext();

  return (
    <>
      <p className={styles['id-card-disclaimer']}>
        {t(LOCIZE_LOGIN_TRANSLATION_KEYS.idCardLoginDisclaimer)}
      </p>

      <AppForm className={styles.form} onSubmit={onLoginFormSubmit}>
        <AppFormButton
          className={styles['submit-button']}
          isSubmit
          label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
        />
        <PrivacyPolicyDisclaimer />
      </AppForm>
    </>
  );
};

const PasswordLoginForm = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { onLoginFormSubmit, loginValidationErrors, loginButtonDisabled } =
    useCreditLineWithdrawalLoginPageContext();

  return (
    <AppForm className={styles.form} onSubmit={onLoginFormSubmit}>
      <AppFormField
        invalid={loginValidationErrors[FormFieldNames.username]}
        name={FormFieldNames.username}
        placeholder={t(LOCIZE_LOGIN_TRANSLATION_KEYS.userNameFieldLabel)}
        type={AppFormFieldTypes.text}
        variant={FormFieldVariants.login}
        visible
      />
      <AppFormField
        invalid={loginValidationErrors[FormFieldNames.password]}
        name={FormFieldNames.password}
        placeholder={t(LOCIZE_LOGIN_TRANSLATION_KEYS.passwordFieldLabel)}
        type={AppFormFieldTypes.password}
        variant={FormFieldVariants.login}
        visible
        withShowAndHidePassword
      />
      <AppFormButton
        className={styles['submit-button']}
        disablingEmptyFieldNames={[
          FormFieldNames.username,
          FormFieldNames.password,
        ]}
        isDisabled={loginButtonDisabled}
        isSubmit
        label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
      />
      <PrivacyPolicyDisclaimer />
    </AppForm>
  );
};

const BankLinkLoginForm = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.login);

  const isLatvianRegionRussianSpecificCopy =
    region === AppRegions.lv && i18n.language === AppLanguages.ru;

  const {
    onLoginFormSubmit,
    loginValidationErrors,
    loginButtonDisabled,
    banklinkOptions,
  } = useCreditLineWithdrawalLoginPageContext();

  return (
    <AppForm className={styles.form} onSubmit={onLoginFormSubmit}>
      <AppFormField
        invalid={loginValidationErrors[FormFieldNames.pin]}
        name={FormFieldNames.pin}
        placeholder={t(
          isLatvianRegionRussianSpecificCopy
            ? LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabelLV
            : LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabel,
        )}
        type={AppFormFieldTypes.text}
        variant={FormFieldVariants.login}
        visible
      />
      {banklinkOptions?.length ? (
        <AppFormField
          banklinkOptions={banklinkOptions}
          invalid={loginValidationErrors[FormFieldNames.paymentMethodKey]}
          name={FormFieldNames.paymentMethodKey}
          type={AppFormFieldTypes.banklink}
          variant={FormFieldVariants.login}
          visible
        />
      ) : (
        <AppLoader
          className={styles['banklink-loader']}
          isRelative
          size={AppLoaderSizes.small}
        />
      )}
      <AppFormButton
        className={styles['submit-button']}
        disablingEmptyFieldNames={[
          FormFieldNames.pin,
          FormFieldNames.paymentMethodKey,
        ]}
        isDisabled={!banklinkOptions?.length || loginButtonDisabled}
        isSubmit
        label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
      />
      <PrivacyPolicyDisclaimer />
    </AppForm>
  );
};

const EParakstsMobileLoginForm = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { onLoginFormSubmit, loginButtonDisabled } =
    useCreditLineWithdrawalLoginPageContext();

  return (
    <AppForm className={styles.form} onSubmit={onLoginFormSubmit}>
      <AppFormButton
        className={styles['submit-button']}
        isDisabled={loginButtonDisabled}
        isSubmit
        label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
      />
      <PrivacyPolicyDisclaimer />
    </AppForm>
  );
};

const EParakstsSmartCardLoginForm = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { onLoginFormSubmit, loginButtonDisabled } =
    useCreditLineWithdrawalLoginPageContext();

  return (
    <AppForm className={styles.form} onSubmit={onLoginFormSubmit}>
      <AppFormButton
        className={styles['submit-button']}
        isDisabled={loginButtonDisabled}
        isSubmit
        label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
      />
      <PrivacyPolicyDisclaimer />
    </AppForm>
  );
};

export const LOGIN_FORMS_MAP = {
  [AppLoginMethods.mobileId]: MobileIdLoginForm,
  [AppLoginMethods.smartId]: SmartIdLoginForm,
  [AppLoginMethods.idCard]: IdCardLoginForm,
  [AppLoginMethods.password]: PasswordLoginForm,
  [AppLoginMethods.banklink]: BankLinkLoginForm,
  [AppLoginMethods.eParakstsMobile]: EParakstsMobileLoginForm,
  [AppLoginMethods.eParakstsSmartCard]: EParakstsSmartCardLoginForm,
};
