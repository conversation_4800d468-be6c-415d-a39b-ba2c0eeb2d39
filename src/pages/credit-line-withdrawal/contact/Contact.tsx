import {
  AppFormFieldTypes,
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  REDIRECT_URLS,
} from 'app-constants';
import {
  AppExternalLink,
  AppForm,
  AppFormButton,
  AppFormField,
  AppLoader,
  AppLoaderContentWrapper,
  AppLocalizationComponent,
} from 'components';
import { CONTACT_INFO, regionPhonePrefix } from 'environment';
import { useContactPageLogic } from 'hooks/page-logic/credit-line-withdrawal';
import type { AnyObject } from 'models';
import { useTranslation } from 'react-i18next';

import styles from './Contact.module.scss';
import { PoliticallyExposedPersonField } from './PoliticallyExposedPersonField';

const Contact = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.contact);
  const { t: tr } = useTranslation(LocizeNamespaces.common);
  const { contactName } = CONTACT_INFO;
  const {
    contactPageLoaded,
    contactPageFormConfig,
    onContactFormSubmit,
    processingContactPage,
    politicalExposureOptions,
    visiblePageAttributes,
    userInfoValidationErrors,
    conditionsAgreement,
  } = useContactPageLogic();

  if (!contactPageLoaded) {
    return <AppLoader isRelative />;
  }

  const termsLabel = (
    <AppLocalizationComponent
      components={{
        site_link: (
          <AppExternalLink
            className="underline cursor-pointer hover:text-primary-brand-02"
            openInNewTab
            to={(REDIRECT_URLS.termsPageUrs as AnyObject)[i18n.language]}
          />
        ),
      }}
      locizeKey={
        LOCIZE_CONTACT_TRANSLATION_KEYS.termsAndConditionsCheckboxLabel
      }
      t={t}
      values={{
        contact_name: contactName,
      }}
    />
  );

  const pensionLabel = (
    <AppLocalizationComponent
      locizeKey={LOCIZE_CONTACT_TRANSLATION_KEYS.pensionCheckboxLabel}
      t={t}
      values={{
        contact_name: contactName,
      }}
    />
  );

  const disablingEmptyFieldNames = [
    // If terms checkbox is visible (user hasn't agreed yet), require it
    ...(!conditionsAgreement ? [FormFieldNames.conditionsAgreement] : []),
    // If pension checkbox is visible, require it
    ...(visiblePageAttributes[PageAttributeNames.pension_checkbox]
      ? [FormFieldNames.allowPensionQuery]
      : []),
  ];

  return (
    <AppLoaderContentWrapper shouldShowLoader={processingContactPage}>
      <AppForm
        className={styles.form}
        formConfig={contactPageFormConfig}
        onSubmit={onContactFormSubmit}
      >
        <AppFormField
          isDisabled
          label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.nameFieldLabel)}
          name={FormFieldNames.name}
          type={AppFormFieldTypes.text}
          visible={visiblePageAttributes[PageAttributeNames.name]}
        />
        <AppFormField
          invalid={userInfoValidationErrors[FormFieldNames.email]}
          label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.emailFieldLabel)}
          name={FormFieldNames.email}
          type={AppFormFieldTypes.email}
          visible={visiblePageAttributes[PageAttributeNames.email]}
        />
        <AppFormField
          invalid={userInfoValidationErrors[FormFieldNames.phone]}
          label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.phoneFieldLabel)}
          name={FormFieldNames.phone}
          prefix={regionPhonePrefix}
          type={AppFormFieldTypes.phone}
          visible={visiblePageAttributes[PageAttributeNames.phoneNumber]}
        />
        <AppFormField
          invalid={userInfoValidationErrors[FormFieldNames.address]}
          label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.addressFieldLabel)}
          name={FormFieldNames.address}
          type={AppFormFieldTypes.text}
          visible={visiblePageAttributes[PageAttributeNames.address]}
        />
        <AppFormField
          invalid={userInfoValidationErrors[FormFieldNames.city]}
          label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.cityFieldLabel)}
          name={FormFieldNames.city}
          type={AppFormFieldTypes.text}
          visible={visiblePageAttributes[PageAttributeNames.city]}
        />
        <AppFormField
          invalid={userInfoValidationErrors[FormFieldNames.postCode]}
          label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.postalCodeFieldLabel)}
          name={FormFieldNames.postCode}
          type={AppFormFieldTypes.text}
          visible={visiblePageAttributes[PageAttributeNames.postalCode]}
        />
        <PoliticallyExposedPersonField
          options={politicalExposureOptions}
          visible={
            visiblePageAttributes[PageAttributeNames.politicalExposureSelect]
          }
        />
        <AppFormField
          invalid={userInfoValidationErrors[FormFieldNames.iban]}
          label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.ibanFieldLabel)}
          name={FormFieldNames.iban}
          tooltipLabel={t(LOCIZE_CONTACT_TRANSLATION_KEYS.ibanTooltipLabel)}
          type={AppFormFieldTypes.text}
          visible={visiblePageAttributes[PageAttributeNames.iban]}
          withTooltip
        />
        <AppFormField
          label={termsLabel}
          name={FormFieldNames.conditionsAgreement}
          type={AppFormFieldTypes.checkbox}
          visible={conditionsAgreement}
        />
        <AppFormField
          label={pensionLabel}
          name={FormFieldNames.allowPensionQuery}
          type={AppFormFieldTypes.checkbox}
          visible={visiblePageAttributes[PageAttributeNames.pension_checkbox]}
        />
        <AppFormField
          label={t(LOCIZE_CONTACT_TRANSLATION_KEYS.newsletterCheckboxLabel, {
            contact_name: contactName,
          })}
          name={FormFieldNames.newsletterAgreement}
          type={AppFormFieldTypes.checkbox}
          visible={visiblePageAttributes[PageAttributeNames.newsletterCheckbox]}
        />
        <AppFormButton
          className={styles['submit-button']}
          disablingEmptyFieldNames={disablingEmptyFieldNames}
          isDisabled={processingContactPage}
          isSubmit
          label={tr(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        />
      </AppForm>
    </AppLoaderContentWrapper>
  );
};

export default Contact;
