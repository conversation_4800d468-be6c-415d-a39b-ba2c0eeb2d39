import { ContactForm } from 'components/contact-form';
import type { ContactFormType } from 'hooks/page-logic/small-loan';
import { useContactPageLogic } from 'hooks/page-logic/small-loan';

const Contact = () => {
  const {
    onContactFormSubmit,
    politicalExposureOptions,
    visiblePageAttributes,
    userInfoValidationErrors,
    form,
    conditionsAgreement,
  } = useContactPageLogic();

  const contextValue = {
    visiblePageAttributes,
    userInfoValidationErrors,
    politicalExposureOptions,
    showPoliticalExposureInfo: true,
  };

  // Wrapper to match the expected signature
  const handleSubmit = async (data: ContactFormType, formInstance?: any) => {
    if (formInstance) {
      return onContactFormSubmit(data, formInstance);
    }
  };

  return (
    <ContactForm<ContactFormType>
      form={form}
      onSubmit={handleSubmit}
      contextValue={contextValue}
      passFormToSubmit={true}
      conditionsAgreement={conditionsAgreement}
    />
  );
};

export default Contact;
