import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { CircleLoader } from 'components/circle-loader';
import { Dialog as AppDialog } from 'components/dialog';
import { AutoNotification } from 'components/notification';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Card } from 'components/ui/card';
import { useRootContext } from 'context/root';
import { useEmtaConsentPageLogic } from 'hooks/page-logic/small-loan';
import EmtaLogoIcon from 'icons/emta-logo.svg?react';
import { Plus } from 'lucide-react';
import { lazy, Suspense, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

const ScoringMethodsDialog = lazy(() =>
  import('./ScoringMethodsDialogSmallLoan').then((module) => ({
    default: module.ScoringMethodsDialogSmallLoan,
  })),
);

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.emtaConsentV2);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const {
    emtaConsentButtonDisabled,
    onEmtaRedirectButtonClick,
    onAccountScoringRedirectButtonClick,
    returnedFromEmtaService,
    isRedirectingToAccountScoring,
    accountScoringButtonDisabled,
    isRedirectingToEmta,
    emtaConsentUrl,
    goBackToScoringMethods,
  } = useEmtaConsentPageLogic();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);

  if (returnedFromEmtaService) {
    return (
      <div>
        <FullScreenLayout>
          <CircleLoader />
          <AutoNotification delay={10000} isAutoAppear className="mb-10">
            <AppLocalizationComponent
              components={{
                button: (
                  <button
                    type="button"
                    onClick={() => {
                      goBackToScoringMethods();
                      setIsDialogOpen(true);
                    }}
                    className="text-primary-brand-02 underline hover:text-primary-brand-02"
                  />
                ),
                external_link: (
                  <AppExternalLink
                    className={cn(
                      'text-primary-brand-02 underline hover:text-primary-brand-02',
                      emtaConsentUrl ? 'cursor-pointer' : 'pointer-events-none',
                    )}
                    to={emtaConsentUrl ?? ''}
                  />
                ),
              }}
              locizeKey={
                LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.problemsVerifyingWithEmtaDisclaimer
              }
              t={t}
            />
          </AutoNotification>

          <Typography variant="xs" className="text-center">
            {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.verifyingFinancialInfo)}
          </Typography>
          <Typography className="mt-4 text-center">
            {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.keepPageOpenDisclaimer)}
          </Typography>

          <Button
            fullWidth
            className="mt-12"
            variant="white"
            loading={pageUrlAndNavigationProcessing}
            onClick={() => setIsConfirmDialogOpen(true)}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
          </Button>
        </FullScreenLayout>
        <AppDialog
          title={t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.confirmTitle)}
          open={isConfirmDialogOpen}
          onOpenChange={setIsConfirmDialogOpen}
        >
          <Typography>
            {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.confirmDescription)}
          </Typography>
          <Button
            className="mt-12"
            variant="black"
            onClick={goBackToScoringMethods}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.confirm)}
          </Button>
        </AppDialog>
      </div>
    );
  }

  return (
    <div>
      <Typography variant="xxs">
        {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.verificationMethodTitle)}
      </Typography>

      <Card className="mt-6 p-6 border-neutral-200">
        <div className="flex items-start space-x-4">
          <EmtaLogoIcon />
          <div className="flex flex-col">
            <Typography variant="xs">
              {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.emtaMethodTitle)}
            </Typography>
            <Typography
              className="text-primary-brand-02"
              variant="text-s"
              affects="medium"
            >
              {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.emtaMethodRecommended)}
            </Typography>
          </div>
        </div>
        <Typography className="mt-6" variant="text-s">
          {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.emtaMethodDescription)}
        </Typography>
        <Button
          fullWidth
          className="mt-10"
          onClick={onEmtaRedirectButtonClick}
          disabled={emtaConsentButtonDisabled}
          loading={isRedirectingToEmta}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>
      </Card>

      <Card
        className="mt-4 flex cursor-pointer items-center justify-between p-4 border-neutral-200"
        onClick={() => setIsDialogOpen(true)}
      >
        <Typography>
          {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.otherMethodsButton)}
        </Typography>
        <Plus className="h-4 w-4 rounded-full border border-primary-black" />
      </Card>

      <Button
        fullWidth
        className="mt-12"
        variant="white"
        loading={pageUrlAndNavigationProcessing}
        onClick={() => {
          getPageUrlAndNavigate(false);
        }}
      >
        {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
      </Button>

      <Suspense fallback={null}>
        <ScoringMethodsDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          isRedirectingToAccountScoring={isRedirectingToAccountScoring}
          accountScoringButtonDisabled={accountScoringButtonDisabled}
          onAccountScoringRedirectButtonClick={
            onAccountScoringRedirectButtonClick
          }
        />
      </Suspense>
    </div>
  );
};

export default Page;
