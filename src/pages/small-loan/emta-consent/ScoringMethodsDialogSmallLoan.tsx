import {
  LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Dialog } from 'components/dialog';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Separator } from 'components/ui/separator';
import { useTranslation } from 'react-i18next';

type ScoringMethodsDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isRedirectingToAccountScoring: boolean;
  onAccountScoringRedirectButtonClick: () => void;
  accountScoringButtonDisabled: boolean;
};

export const ScoringMethodsDialogSmallLoan = ({
  open,
  onOpenChange,
  isRedirectingToAccountScoring,
  onAccountScoringRedirectButtonClick,
  accountScoringButtonDisabled,
}: ScoringMethodsDialogProps) => {
  const { t } = useTranslation(LocizeNamespaces.emtaConsentV2);

  return (
    <Dialog
      title={t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.otherMethodsTitle)}
      open={open}
      onOpenChange={onOpenChange}
    >
      <Typography>
        {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.otherMethodsDescription)}
      </Typography>

      <Separator className="my-6" />

      <section className="space-y-4">
        <Typography variant="xxs">
          {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.internetBankTitle)}
        </Typography>
        <Typography variant="text-s">
          {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.internetBankDescription)}
        </Typography>
        <Button
          size="small"
          className="mt-6"
          loading={isRedirectingToAccountScoring}
          disabled={accountScoringButtonDisabled}
          onClick={onAccountScoringRedirectButtonClick}
        >
          {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.startButton)}
        </Button>
      </section>
    </Dialog>
  );
};
