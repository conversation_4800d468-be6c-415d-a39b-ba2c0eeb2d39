import {
  AppLanguages,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_REJECT_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { useRejectPageLogic } from 'hooks/page-logic/small-loan';
import CircleIcon from 'icons/circle-x.svg?react';
import credy from 'img/credy.png';
import type { AnyObject } from 'models';
import { Trans, useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.reject);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const {
    visiblePageAttributes,
    returnToMerchantButtonDisabled,
    onReturnToMerchantButtonClick,
    onGoToFAQButtonClick,
    onEstoAccountButtonClick,
    isRedirectingToCustomerProfile,
    onForwardLoanClick,
    isForwardLoadLinkProcessing,
  } = useRejectPageLogic();

  const shouldShowButtonsSection = !(
    !visiblePageAttributes[
      PageAttributeNames.frequentlyAskedQuestionsSection
    ] && !visiblePageAttributes[PageAttributeNames.returnToMerchantButton]
  );

  return (
    <FullScreenLayout>
      {visiblePageAttributes[PageAttributeNames.forwardRejectsSection] ? (
        <>
          <div className="w-28 mb-6">
            <img alt="Credy" src={credy} />
          </div>
          <div className="text-center">
            <Typography variant="xs" className="text-center mb-4">
              {t(LOCIZE_REJECT_TRANSLATION_KEYS.withCredyHeading)}
            </Typography>
            <Trans
              i18nKey={LOCIZE_REJECT_TRANSLATION_KEYS.withCredyDisclaimer}
              t={t}
            />
            <Button
              loading={isForwardLoadLinkProcessing}
              className="mt-12"
              fullWidth
              variant="black"
              onClick={onForwardLoanClick}
            >
              {t(LOCIZE_REJECT_TRANSLATION_KEYS.withCredyViewOffersButton)}
            </Button>
            <AppLocalizationComponent
              className={'text-neutral-500 mt-4 text-sm'}
              components={{
                site_link: (
                  <AppExternalLink
                    to={
                      (REDIRECT_URLS.credyTermsPageUrs as AnyObject)[
                        AppLanguages.lt
                      ]
                    }
                  />
                ),
              }}
              locizeKey={LOCIZE_REJECT_TRANSLATION_KEYS.credyDisclaimer}
              t={t}
            />
          </div>
        </>
      ) : (
        <>
          <div className=" flex items-center justify-center rounded-full size-18 bg-stone-50 mb-6">
            <CircleIcon className="size-8" />
          </div>

          <Typography variant="xs" className="text-center">
            {t(LOCIZE_REJECT_TRANSLATION_KEYS.smallLoanRejectionHeading2)}
          </Typography>
          <Typography className="mt-4 text-center">
            {t(LOCIZE_REJECT_TRANSLATION_KEYS.smallLoanRejectionDisclaimer)}
          </Typography>
        </>
      )}

      {shouldShowButtonsSection ? (
        <div className="w-full flex flex-col gap-4 mt-12">
          {visiblePageAttributes[
            PageAttributeNames.frequentlyAskedQuestionsSection
          ] ? (
            <Button
              className={cn(
                visiblePageAttributes[
                  PageAttributeNames.forwardRejectsSection
                ] && 'underline',
              )}
              onClick={onGoToFAQButtonClick}
              variant={
                visiblePageAttributes[PageAttributeNames.forwardRejectsSection]
                  ? 'white'
                  : 'black'
              }
              fullWidth
            >
              {t(LOCIZE_REJECT_TRANSLATION_KEYS.rejectionReasonsButton)}
            </Button>
          ) : null}

          {visiblePageAttributes[PageAttributeNames.returnToMerchantButton] ? (
            <Button
              disabled={returnToMerchantButtonDisabled}
              onClick={onReturnToMerchantButtonClick}
              variant="blue"
              fullWidth
            >
              {t(LOCIZE_REJECT_TRANSLATION_KEYS.returnToMerchant)}
            </Button>
          ) : null}

          {visiblePageAttributes[PageAttributeNames.estoAccountSection] ? (
            <Button
              loading={isRedirectingToCustomerProfile}
              onClick={onEstoAccountButtonClick}
              variant="grey"
              fullWidth
            >
              {tc(LOCIZE_COMMON_TRANSLATION_KEYS.goToEstoAccountButton)}
            </Button>
          ) : null}
        </div>
      ) : null}
    </FullScreenLayout>
  );
};

export default Page;
