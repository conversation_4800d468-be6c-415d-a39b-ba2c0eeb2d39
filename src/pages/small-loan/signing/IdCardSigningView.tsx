import IdCardReaderPendingComponent from 'components/id-card-reader-pending-component/IdCardReaderPendingComponent';
import { useSigningPageContext } from 'context/small-loan';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

export const IdCardSigningView = () => {
  const { onIdCardSigningCancel, signContractWithIDCardProcessing } =
    useSigningPageContext();

  const onCancel = () => {
    if (onIdCardSigningCancel) {
      onIdCardSigningCancel();
    }
  };
  return (
    <FullScreenLayout>
      <IdCardReaderPendingComponent
        onCancel={onCancel}
        isCancelButtonVisible={!signContractWithIDCardProcessing}
      />
    </FullScreenLayout>
  );
};
