import { SigningPageViewTypes } from 'app-constants';
import { useSigningPageContext } from 'context/small-loan';
import { SigningPageLogicProvider } from 'context/small-loan/SigningPageLogicProvider';
import { lazy, Suspense, useEffect } from 'react';

const PendingView = lazy(() =>
  import('../../../components/default-loader/DefaultLoader').then((module) => ({
    default: module.DefaultLoader,
  })),
);

const SigningView = lazy(() =>
  import('./SigningView').then((module) => ({
    default: module.SigningView,
  })),
);

const PinConfirmationView = lazy(() =>
  import('./PinConfirmationView').then((module) => ({
    default: module.PinConfirmationView,
  })),
);

const SigningContractView = lazy(() =>
  import('./SigningContractView').then((module) => ({
    default: module.SigningContractView,
  })),
);

const IdCardSigningView = lazy(() =>
  import('./IdCardSigningView').then((module) => ({
    default: module.IdCardSigningView,
  })),
);

const PayseraSigningMethodView = lazy(() =>
  import('./BanklinkSigningView').then((module) => ({
    default: module.BanklinkSigningView,
  })),
);

const SigningFailedView = lazy(() =>
  import('./SigningFailedView').then((module) => ({
    default: module.SigningFailedView,
  })),
);

const Page = () => {
  const { signingPageViewType } = useSigningPageContext();

  useEffect(() => {
    if (signingPageViewType === SigningPageViewTypes.preparing) {
      import('./PinConfirmationView').then(() => {});
    }

    if (signingPageViewType === SigningPageViewTypes.pinConfirmation) {
      import('./SigningContractView').then(() => {});
    }
  }, [signingPageViewType]);

  const renderSigningPageContent = () => {
    switch (signingPageViewType) {
      case SigningPageViewTypes.banklinkSigning:
        return <PayseraSigningMethodView />;
      case SigningPageViewTypes.idCardSigning:
        return <IdCardSigningView />;
      case SigningPageViewTypes.preparing:
      case SigningPageViewTypes.signingContract:
        return <SigningContractView />;
      case SigningPageViewTypes.pinConfirmation:
        return <PinConfirmationView />;
      case SigningPageViewTypes.signing:
        return <SigningView />;
      case SigningPageViewTypes.pending:
        return <PendingView />;
      case SigningPageViewTypes.signingFailed:
        return <SigningFailedView />;
      default:
        return <SigningView />;
    }
  };

  // Provide a stable fallback that matches the current view context
  const getFallbackComponent = () => {
    // If we're transitioning from preparing to pinConfirmation,
    // show the preparing view as fallback to avoid flicker
    if (signingPageViewType === SigningPageViewTypes.pinConfirmation) {
      return <SigningContractView />;
    }
    return <SigningContractView />;
  };

  return (
    <Suspense fallback={getFallbackComponent()}>
      {renderSigningPageContent()}
    </Suspense>
  );
};

const SigningPage = () => (
  <SigningPageLogicProvider>
    <Page />
  </SigningPageLogicProvider>
);

export default SigningPage;
