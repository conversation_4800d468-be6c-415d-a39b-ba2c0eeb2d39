import { Separator } from '@radix-ui/react-dropdown-menu';
import {
  Currencies,
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
  MONTHS_IN_YEAR,
  PageAttributeNames,
  PRICING_KEYS,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { FormInputField } from 'components/form/form-input-field';
import { LogoutButton } from 'components/logout-button';
import { Notification } from 'components/notification';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from 'components/ui/card';
import { Form } from 'components/ui/form';
import { useSigningPageContext } from 'context/small-loan';
import type { SigningFormType } from 'hooks/page-logic/small-loan';
import { useGetPageUrl } from 'hooks/use-get-page-url';
import { Tag } from 'lucide-react';
import type { AnyObject } from 'models';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { roundNumberUpByTwoDecimals } from 'services/math-service';
import { formatNumber } from 'utils';
import { cn } from 'utils/tailwind';

export const SigningView = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.signing);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const {
    contractLink,
    userCanSignContract,
    processingSigningPage,
    form,
    onSigningFormSubmit,
    signingPageFormConfig,
    userInfoValidationErrors,
    isPayseraSigningMethodDialogOpen,
    visiblePageAttributes,
    applicationCampaignUpdating,
    isApplicationPayoutMethodUpdating,
    application,
    pricingData,
  } = useSigningPageContext();

  const {
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing: pageUrlAndNavigationProcessingBackButton,
  } = useGetPageUrl();

  const paymentLeaveMonths = application?.campaign?.payment_leave_months ?? 0;

  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = () => {
    setIsDownloading(true);
    window.location.href = contractLink;

    setTimeout(() => {
      setIsDownloading(false);
    }, 2000);
  };

  const shouldShowNoSigningFeeCampaign =
    visiblePageAttributes[PageAttributeNames.fixedZeroContractFeeDisclaimer];

  const shouldShowStartPaymentsLaterCampaign =
    paymentLeaveMonths > 0 &&
    visiblePageAttributes[
      PageAttributeNames.radioButtonContainerStartPaymentAfterMonths
    ];

  const shouldShowInstantPayoutOption =
    application.is_instant_payout_available &&
    pricingData[PRICING_KEYS.smallLoanInstantPayoutFee] > 0;

  const applicationProcessing =
    applicationCampaignUpdating ||
    isApplicationPayoutMethodUpdating ||
    processingSigningPage;

  return (
    <Form {...signingPageFormConfig}>
      <form
        onSubmit={form.handleSubmit(onSigningFormSubmit)}
        className="grid w-full gap-2"
      >
        <Card className="w-full max-w-md rounded-3xl shadow-none border border-solid border-transparent md:border-neutral-200 md:shadow-container">
          <CardHeader className="p-0 md:p-6 md:pb-0">
            <div className="flex justify-between items-center">
              <Typography variant="xxs">
                {t(LOCIZE_SIGNING_TRANSLATION_KEYS.contractTitle)}
              </Typography>
              <Button
                variant="transparent"
                className="hover:bg-transparent p-0"
                onClick={handleDownload}
                loading={isDownloading}
                disabled={applicationProcessing}
              >
                <Typography
                  variant="text-s"
                  affects="link"
                  className="font-normal hover:text-primary-brand-02"
                >
                  {t(LOCIZE_SIGNING_TRANSLATION_KEYS.viewTerms)}
                </Typography>
              </Button>
            </div>
          </CardHeader>

          <CardContent className="mt-4 p-0 md:p-6 md:pt-0">
            {(shouldShowNoSigningFeeCampaign ||
              shouldShowStartPaymentsLaterCampaign) && (
              <div className="mt-4">
                <Separator className="h-px w-full bg-neutral-200" />
                <Typography variant="text-s" className="mt-4 text-neutral-500">
                  {t(LOCIZE_SIGNING_TRANSLATION_KEYS.campaignOfferTitle)}
                </Typography>

                <div className="my-2 flex flex-col gap-2">
                  {shouldShowNoSigningFeeCampaign && (
                    <div className="flex items-center justify-between p-3.5 bg-green-50 rounded-xl">
                      <div className="flex items-center gap-3">
                        <Tag className="w-5 h-5 flex items-center justify-center rotate-90" />
                        <Typography variant="text-s" affects="medium">
                          {t(
                            LOCIZE_SIGNING_TRANSLATION_KEYS.campaignNoSigningFee,
                          )}
                        </Typography>
                      </div>
                    </div>
                  )}

                  {shouldShowStartPaymentsLaterCampaign && (
                    <FormCheckboxField<SigningFormType>
                      name={FormFieldNames.paymentLeave}
                      control={signingPageFormConfig.control}
                      label={
                        <Typography
                          variant="text-s"
                          affects="medium"
                          className="translate-y-[1.5px] text-gray-500"
                        >
                          {tc(
                            LOCIZE_COMMON_TRANSLATION_KEYS.specialOfferPaymentLeaveTitle,
                          )}
                        </Typography>
                      }
                      disabled={applicationCampaignUpdating}
                      info={tc(
                        LOCIZE_COMMON_TRANSLATION_KEYS.specialOfferPaymentLeaveSmallLoanDescription,
                        {
                          numberOfMonths: `${paymentLeaveMonths + 1} ${tc(
                            paymentLeaveMonths + 1 === 1
                              ? LOCIZE_COMMON_TRANSLATION_KEYS.monthTitle
                              : LOCIZE_COMMON_TRANSLATION_KEYS.monthsTitle,
                          )}`,
                          merchantName: '',
                        },
                      )}
                      className={cn(
                        'w-5 h-5 rounded border border-black bg-white',
                        applicationCampaignUpdating && 'border-neutral-400!',
                      )}
                      containerClassName={cn(
                        'px-2.5 pt-3 pb-2 bg-green-50 rounded-xl [&>div>div>div]:-translate-y-[2px] [&>div>div>div>div>svg>g>path]:fill-black',
                        applicationCampaignUpdating && 'bg-neutral-100',
                      )}
                    />
                  )}
                </div>
              </div>
            )}

            {application.credit_info?.contract_fee === 0 && (
              <div className="my-4">
                <Separator className="h-px w-full bg-neutral-200" />
                <div className="flex justify-between items-center pt-4">
                  <Typography className="text-neutral-500" variant="text-s">
                    {tc(LOCIZE_COMMON_TRANSLATION_KEYS.signingFeeTitle)}
                  </Typography>
                  <Typography variant="text-s">{`${application.credit_info.contract_fee} ${Currencies.euro}`}</Typography>
                </div>
              </div>
            )}

            {application.credit_info?.management_fee === 0 && (
              <div className="mt-4">
                <Separator className="h-px w-full bg-neutral-200" />
                <div className="flex justify-between items-center py-4">
                  <Typography className="text-neutral-500" variant="text-s">
                    {tc(LOCIZE_COMMON_TRANSLATION_KEYS.managementFeeTitle)}
                  </Typography>
                  <Typography variant="text-s">{`${application.credit_info.management_fee} ${Currencies.euro}`}</Typography>
                </div>
              </div>
            )}

            <Separator className="h-px w-full bg-neutral-200" />

            <div className="flex justify-between items-center py-4">
              <Typography className="text-neutral-500" variant="text-s">
                {tc(LOCIZE_COMMON_TRANSLATION_KEYS.monthlyInterestTitle)}
              </Typography>
              <Typography variant="text-s">{`${roundNumberUpByTwoDecimals(
                (application?.credit_info?.annual_pct_rate ?? 0) /
                  MONTHS_IN_YEAR,
              )}%`}</Typography>
            </div>

            <Separator className="h-px w-full bg-neutral-200" />

            <div className="flex justify-between items-center py-4">
              <Typography className="text-neutral-500" variant="text-s">
                {tc(LOCIZE_COMMON_TRANSLATION_KEYS.monthlyPaymentTitle)}
              </Typography>
              <Typography variant="text-s">
                {`${formatNumber({ value: application?.credit_info?.monthly_payment ?? 0, minimumFractionDigits: 2 })} ${Currencies.euro}`}
              </Typography>
            </div>

            <FormInputField<SigningFormType>
              name={FormFieldNames.iban}
              control={signingPageFormConfig.control}
              label={t(LOCIZE_SIGNING_TRANSLATION_KEYS.ibanFieldLabel)}
              invalid={
                userInfoValidationErrors[FormFieldNames.iban] ||
                !!signingPageFormConfig.getFieldState?.(FormFieldNames.iban)
                  ?.error
              }
              info={t(LOCIZE_SIGNING_TRANSLATION_KEYS.ibanTooltipLabel)}
              disabled={processingSigningPage}
              containerClassName="mt-2"
            />

            {shouldShowInstantPayoutOption && (
              <FormCheckboxField<SigningFormType>
                name={FormFieldNames.instantPayout}
                control={signingPageFormConfig.control}
                label={t(
                  LOCIZE_SIGNING_TRANSLATION_KEYS.instantPayoutMethodTypeTitle,
                )}
                disabled={isApplicationPayoutMethodUpdating}
                info={t(
                  LOCIZE_SIGNING_TRANSLATION_KEYS.instantPayoutMethodTypeDescription,
                  { fee: pricingData[PRICING_KEYS.smallLoanInstantPayoutFee] },
                )}
                className={cn(
                  'w-5 h-5 rounded border border-black',
                  isApplicationPayoutMethodUpdating && 'border-neutral-400!',
                )}
                containerClassName={cn(
                  'mt-2 px-2.5 pt-3 pb-2 bg-white border border-neutral-200 rounded-xl [&>div>div>div]:-translate-y-[2px] [&>div>div>div>div>svg>g>path]:fill-black',
                  isApplicationPayoutMethodUpdating && 'bg-neutral-100',
                )}
              />
            )}

            <div className="mt-6">
              <Typography
                className="text-center text-neutral-500"
                variant="text-s"
                tag="div"
              >
                <AppLocalizationComponent
                  components={{
                    site_link: (
                      <AppExternalLink
                        className="underline hover:text-primary-brand-02"
                        to={
                          (REDIRECT_URLS.termsPageUrs as AnyObject)[
                            i18n.language
                          ]
                        }
                      />
                    ),
                    contract_link: (
                      <AppExternalLink
                        className={cn(
                          'underline hover:text-primary-brand-02',
                          applicationProcessing &&
                            'pointer-events-none opacity-50',
                        )}
                        openInNewTab={false}
                        to={contractLink}
                      />
                    ),
                  }}
                  locizeKey={
                    LOCIZE_SIGNING_TRANSLATION_KEYS.acceptTermsOfContract
                  }
                  t={t}
                />
              </Typography>
            </div>
          </CardContent>

          <CardFooter className="flex-col gap-6 pt-6 px-0 pb-0 md:p-6 md:pt-0">
            {userCanSignContract ? (
              <Button
                type="submit"
                fullWidth
                variant="black"
                disabled={
                  pageUrlAndNavigationProcessingBackButton ||
                  isApplicationPayoutMethodUpdating ||
                  applicationCampaignUpdating
                }
                loading={
                  !isPayseraSigningMethodDialogOpen && processingSigningPage
                }
              >
                {t(LOCIZE_SIGNING_TRANSLATION_KEYS.signContract)}
              </Button>
            ) : (
              <>
                <Notification>
                  {t(
                    LOCIZE_SIGNING_TRANSLATION_KEYS.changeLoginMethodDisclaimer,
                  )}
                </Notification>
                <LogoutButton variant="black" fullWidth>
                  {t(
                    LOCIZE_SIGNING_TRANSLATION_KEYS.changeLoginMethodButtonLabel,
                  )}
                </LogoutButton>
              </>
            )}
          </CardFooter>
        </Card>

        <Button
          loading={pageUrlAndNavigationProcessingBackButton}
          fullWidth
          variant="white"
          className="mt-2"
          onClick={() => {
            getPageUrlAndNavigate(false);
          }}
          disabled={processingSigningPage}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
      </form>
    </Form>
  );
};
