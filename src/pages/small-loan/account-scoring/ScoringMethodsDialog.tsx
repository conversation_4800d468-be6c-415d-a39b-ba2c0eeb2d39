import {
  ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES,
  AppRegions,
  LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Dialog } from 'components/dialog';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Input } from 'components/ui/input';
import { Separator } from 'components/ui/separator';
import { region } from 'environment';
import PaperClipIcon from 'icons/paper-clip.svg?react';
import TrashIcon from 'icons/trash.svg?react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

const ACCEPTED_FILE_TYPES =
  ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES.join(', ');

type ScoringMethodsDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAccountStatementUploaded: (statement: File, callback?: () => void) => void;
  uploadAccountStatementProcessing: boolean;
  isEmtaButtonDisabled: boolean;
  isRedirectingToEmta: boolean;
  onEmtaRedirectButtonClick: () => void;
};

const isEmtaSectionVisible = region === AppRegions.et;

export const ScoringMethodsDialog = ({
  open,
  onOpenChange,
  isRedirectingToEmta,
  uploadAccountStatementProcessing,
  onAccountStatementUploaded,
  onEmtaRedirectButtonClick,
  isEmtaButtonDisabled,
}: ScoringMethodsDialogProps) => {
  const { t } = useTranslation(LocizeNamespaces.accountScoringV2);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const [uploadedStatement, setUploadedStatement] = useState<File | null>(null);

  return (
    <Dialog
      title={t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.otherMethodsTitle)}
      open={open}
      onOpenChange={onOpenChange}
    >
      <Typography>
        {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.otherMethodsDescription)}
      </Typography>

      <Separator className="my-6" />

      {isEmtaSectionVisible && (
        <>
          <section className="space-y-4">
            <Typography variant="xxs">
              {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.emtaTitle)}
            </Typography>
            <Typography variant="text-s">
              {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.emtaDescription)}
            </Typography>
            <Button
              size="small"
              className="mt-6"
              loading={isRedirectingToEmta}
              disabled={isEmtaButtonDisabled}
              onClick={() => {
                onEmtaRedirectButtonClick();
                onOpenChange(false);
              }}
            >
              {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.startButton)}
            </Button>
          </section>
          <Separator className="my-6" />
        </>
      )}

      <section className="space-y-4">
        <Typography variant="xxs">
          {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.uploadBankStatementTitle)}
        </Typography>
        <Typography variant="text-s">
          {t(
            LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.uploadBankStatementDescription,
          )}
        </Typography>
        <Typography variant="text-s" className="text-neutral-500 mt-4">
          {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.acceptedFileTypesLabel, {
            acceptedFileTypes: ACCEPTED_FILE_TYPES,
          })}
        </Typography>

        {uploadedStatement && (
          <div className="flex items-center justify-between rounded-lg border border-neutral-200 p-3">
            <div className="flex items-center space-x-3">
              <PaperClipIcon className="h-5 w-5 text-neutral-500" />
              <Typography variant="text-s" className="truncate">
                {uploadedStatement.name}
              </Typography>
            </div>
            <button
              type="button"
              onClick={() => setUploadedStatement(null)}
              className="text-neutral-500 hover:text-neutral-700"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        )}

        <Input
          className="hidden"
          id="file-upload"
          type="file"
          accept={ACCEPTED_FILE_TYPES}
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              setUploadedStatement(file);
            }
          }}
        />
        {!uploadedStatement && (
          <Button
            size="small"
            className="mt-6!"
            onClick={() => {
              document.getElementById('file-upload')?.click();
            }}
          >
            {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.uploadButton)}
          </Button>
        )}
        {uploadedStatement && (
          <Button
            size="small"
            className="mt-6!"
            loading={uploadAccountStatementProcessing}
            onClick={() => {
              onAccountStatementUploaded(uploadedStatement, () => {
                onOpenChange(false);
              });
            }}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
          </Button>
        )}
      </section>
    </Dialog>
  );
};
