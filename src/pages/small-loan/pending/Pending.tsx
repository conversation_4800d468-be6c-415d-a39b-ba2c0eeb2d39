import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_PENDING_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { SpinnerLoader } from 'components/spinner-loader';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { usePendingPageLogic } from 'hooks/page-logic/small-loan';
import { Trans, useTranslation } from 'react-i18next';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.pending);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const {
    visiblePageAttributes,
    onEstoAccountButtonClick,
    onCheckIncomeButtonClick,
    pageUrlAndNavigationProcessing,
    debtAmount,
    onPayDebtButtonClick,
    onCreditLineOnboardingButtonClick,
    onCreditLineWithdrawalOnboardingButtonClick,
    isRedirecting,
    availableCreditLimit,
    creditLimit,
  } = usePendingPageLogic();

  return (
    <FullScreenLayout>
      <div className="flex flex-col items-center justify-center h-20 w-20 bg-[#F8F5E1] rounded-full mb-6">
        <SpinnerLoader />
      </div>

      {visiblePageAttributes[PageAttributeNames.defaultPendingDisclaimer] ? (
        <>
          <Typography variant="xs" className="text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingApplicationInReview)}
          </Typography>
          <Typography className="mt-4 text-center">
            {t(
              LOCIZE_PENDING_TRANSLATION_KEYS.smallLoanDefaultPendingDisclaimer,
            )}
          </Typography>
        </>
      ) : null}

      {visiblePageAttributes[PageAttributeNames.cawAvailableUpToDisclaimer] ? (
        <>
          <Typography variant="xs" className="text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingDisclaimer)}
          </Typography>
          <Typography className="mt-4 text-center">
            {availableCreditLimit && (
              <Trans
                i18nKey={
                  LOCIZE_PENDING_TRANSLATION_KEYS.creditLineWithdrawalOnboardingDisclaimer
                }
                t={t}
                values={{ availableCreditLimit }}
              />
            )}
          </Typography>
        </>
      ) : null}

      {visiblePageAttributes[
        PageAttributeNames.unsignedCaWithLimitDisclaimer
      ] ? (
        <>
          <Typography variant="xs" className="text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingApplicationInReview)}
          </Typography>
          <Typography className="mt-4 text-center">
            {creditLimit && (
              <Trans
                i18nKey={
                  LOCIZE_PENDING_TRANSLATION_KEYS.creditLineOnboardingDisclaimer
                }
                t={t}
                values={{ creditLimit }}
              />
            )}
          </Typography>
        </>
      ) : null}

      {visiblePageAttributes[PageAttributeNames.checkIncomeDisclaimer] ? (
        <>
          <Typography variant="xs" className="text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingCheckIncomeHeading)}
          </Typography>
          <Typography className="mt-4 text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingCheckIncomeDisclaimer)}
          </Typography>
        </>
      ) : null}

      {visiblePageAttributes[
        PageAttributeNames.waitingSpouseConsentPendingDisclaimer
      ] ? (
        <>
          <Typography variant="xs" className="text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingApplicationInReview)}
          </Typography>
          <Typography className="mt-4 text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.spouseConsentPendingDisclaimer)}
          </Typography>
        </>
      ) : null}

      {visiblePageAttributes[
        PageAttributeNames.manualScoringNeededDisclaimer
      ] ? (
        <>
          <Typography variant="xs" className="text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingApplicationInReview)}
          </Typography>
          <Typography className="mt-4 text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.manualScoringNeededDisclaimer)}
          </Typography>
        </>
      ) : null}

      {visiblePageAttributes[
        PageAttributeNames.outsideWorkingHoursPendingDisclaimer
      ] ? (
        <>
          <Typography variant="xs" className="text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingApplicationInReview)}
          </Typography>
          <Typography className="mt-4 text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.pendingWorkingHoursDisclaimer)}
          </Typography>
        </>
      ) : null}

      {visiblePageAttributes[PageAttributeNames.overdueDisclaimer] ? (
        <>
          <Typography variant="xs" className="text-center">
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.debtHeading)}
          </Typography>
          <Typography className="mt-4 text-center">
            {debtAmount && (
              <Trans
                i18nKey={LOCIZE_PENDING_TRANSLATION_KEYS.debtDisclaimer}
                t={t}
                values={{ debt: debtAmount }}
              />
            )}
          </Typography>
        </>
      ) : null}

      <div className="flex flex-col gap-4 w-full mt-12">
        {visiblePageAttributes[
          PageAttributeNames.unsignedCaWithLimitDisclaimer
        ] ? (
          <Button
            fullWidth
            onClick={onCreditLineOnboardingButtonClick}
            variant={'black'}
            disabled={isRedirecting}
          >
            {t(
              LOCIZE_PENDING_TRANSLATION_KEYS.creditLineOnboardingCTAButtonLabel,
            )}
          </Button>
        ) : null}

        {visiblePageAttributes[
          PageAttributeNames.cawAvailableUpToDisclaimer
        ] ? (
          <Button
            onClick={onCreditLineWithdrawalOnboardingButtonClick}
            variant={'black'}
            loading={isRedirecting}
          >
            {t(
              LOCIZE_PENDING_TRANSLATION_KEYS.creditLineWithdrawalOnboardingCTAButtonLabel,
            )}
          </Button>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.estoAccountSection] ? (
          <Button
            onClick={onEstoAccountButtonClick}
            variant={'black'}
            loading={isRedirecting}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.estoAccount)}
          </Button>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.checkIncomeButton] ? (
          <Button
            disabled={pageUrlAndNavigationProcessing}
            onClick={onCheckIncomeButtonClick}
            variant={'black'}
            loading={isRedirecting}
          >
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.checkIncomeButtonLabel)}
          </Button>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.overdueDisclaimer] ? (
          <Button
            onClick={onPayDebtButtonClick}
            variant={'black'}
            loading={isRedirecting}
          >
            {t(LOCIZE_PENDING_TRANSLATION_KEYS.payDebtButton)}
          </Button>
        ) : null}
      </div>
    </FullScreenLayout>
  );
};

export default Page;
