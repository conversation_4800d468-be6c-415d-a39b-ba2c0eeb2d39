import type { CreditSettingCalculator } from 'api/core/generated';
import { Currencies, DEFAULT_DIGITS_AFTER_DECIMAL_POINT } from 'app-constants';
import { Typography } from 'components/typography';
import Skeleton from 'react-loading-skeleton';
import { cn } from 'utils/tailwind';

type CheckoutAmountHeadingProps = {
  title: string;
  description: string;
  className?: string;
  loading?: boolean;
  creditSettings: Array<CreditSettingCalculator> | null;
};

const CheckoutAmountHeading = ({
  title,
  description,
  className,
  loading,
  creditSettings,
}: CheckoutAmountHeadingProps) => {
  const monthlyPayment = creditSettings?.[0]?.monthly_payment ?? 0;

  return (
    <div className={cn('flex flex-col gap-4', className)}>
      <div className="flex justify-between items-baseline">
        <Typography variant="text-l">{title}</Typography>
        <div className="[&>span:first-child]:absolute [&>span:first-child]:top-0 [&>span:first-child]:left-0 [&>span:first-child]:w-full relative font-bold text-[1.875rem]">
          {loading && (
            <Skeleton className="rounded-3xl h-7.5 animate-pulse-opacity" />
          )}
          <div className={cn(loading && 'text-transparent')}>
            {`${monthlyPayment.toFixed(DEFAULT_DIGITS_AFTER_DECIMAL_POINT)} ${Currencies.euro}`}
          </div>
        </div>
      </div>

      <Typography variant="text-s" className="text-neutral-500">
        {description}
      </Typography>
    </div>
  );
};

export default CheckoutAmountHeading;
