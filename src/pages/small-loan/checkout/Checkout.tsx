import {
  FormFieldNames,
  LOCIZE_CHECKOUT_TRANSLATION_KEYS,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { FormPeriodField } from 'components/form/form-period-field/FormPeriodField';
import {
  FormRangeInputField,
  FormRangeInputFieldDefaultValueOutputFormat,
} from 'components/form/form-range-input-field/FormRangeInputField';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { useCheckoutPageLogic } from 'hooks/page-logic/small-loan';
import { useIsMobileView } from 'hooks/system';
import { useTranslation } from 'react-i18next';

import { SmallLoanTitle } from '../root/ui/SmallLoanTitle';
import CheckoutAmountHeading from './ChekoutAmountHeading';
import { LoanAmountCustomValueOutput } from './LoanAmountCustomValueOutput';

const STEP = 10;

const Checkout = () => {
  const {
    form,
    onCheckoutPageFormSubmit,
    possiblePeriods,
    minLoanAmount,
    maxLoanAmount,
    onLoanAmountChange,
    onPeriodChange,
    nextButtonIsDisabled,
    creditSettings,
    creditSettingsLoading,
  } = useCheckoutPageLogic();

  const { t } = useTranslation(LocizeNamespaces.checkout);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const isMobileView = useIsMobileView();

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onCheckoutPageFormSubmit)}
        className="w-full"
      >
        {!isMobileView && <SmallLoanTitle className="text-[1.875rem] mb-4" />}

        <div className={'mb-10'}>
          <FormRangeInputField
            onAmountChange={onLoanAmountChange}
            onSliderDragEnd={onLoanAmountChange}
            control={form.control}
            name={FormFieldNames.netTotal}
            min={minLoanAmount}
            max={maxLoanAmount}
            step={STEP}
            disabled={form.formState.isSubmitting}
            size={isMobileView ? 'regular' : 'small'}
            withInput
            defaultValueOutputFormat={
              FormRangeInputFieldDefaultValueOutputFormat.FORM_INPUT
            }
            customValueOutput={
              isMobileView ? (
                <LoanAmountCustomValueOutput
                  onAmountChange={onLoanAmountChange}
                  step={STEP}
                  min={minLoanAmount}
                  max={maxLoanAmount}
                />
              ) : undefined
            }
            outputValueInputLabel={t(
              LOCIZE_CHECKOUT_TRANSLATION_KEYS.amountInputLabel,
            )}
          />
        </div>

        <div className={'mt-4'}>
          <FormPeriodField
            className={'mt-4'}
            control={form.control}
            label={tc(LOCIZE_COMMON_TRANSLATION_KEYS.periodTitle)}
            name={FormFieldNames.periodMonths}
            periodValues={possiblePeriods}
            onPeriodChange={onPeriodChange}
            disabled={form.formState.isSubmitting}
            size={isMobileView ? 'regular' : 'small'}
          />
        </div>

        <CheckoutAmountHeading
          creditSettings={creditSettings}
          loading={creditSettingsLoading}
          className="mt-10"
          title={t(
            LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanMonthlyPaymentTitle,
          )}
          description={t(
            LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanMonthlyPaymentDescription,
          )}
        />

        <Button
          className="mt-10 mb-6"
          type="submit"
          fullWidth
          loading={form.formState.isSubmitting}
          disabled={nextButtonIsDisabled || !form.formState.isValid}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>
      </form>
    </Form>
  );
};

export default Checkout;
