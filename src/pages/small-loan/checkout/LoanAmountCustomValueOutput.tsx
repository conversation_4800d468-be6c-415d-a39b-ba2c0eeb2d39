import {
  Currencies,
  FormFieldNames,
  LOCIZE_CHECKOUT_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Typography } from 'components/typography';
import { useDebounce } from 'hooks/utils';
import { PencilIcon } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import type { FieldValues } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { NumericFormat } from 'react-number-format';
import { cn } from 'utils/tailwind';

type LoanAmountCustomValueOutputProps = {
  min: number;
  max: number;
  step: number;
  onAmountChange: (formFieldValues: FieldValues) => void;
};

export const LoanAmountCustomValueOutput = ({
  min,
  max,
  step,
  onAmountChange,
}: LoanAmountCustomValueOutputProps) => {
  const { t } = useTranslation(LocizeNamespaces.checkout);
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();
  const loanAmountValue = watch(FormFieldNames.netTotal);

  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const debouncedOnAmountChange = useDebounce((loanAmountValue: number) => {
    onAmountChange?.({ [FormFieldNames.netTotal]: loanAmountValue });
  }, 400);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleBlur = () => {
    let finalValue = loanAmountValue;

    if (!loanAmountValue) {
      finalValue = min;
    } else if (loanAmountValue < min) {
      finalValue = min;
    } else if (loanAmountValue > max) {
      finalValue = max;
    } else if (loanAmountValue % step !== 0) {
      finalValue = +(Math.ceil(loanAmountValue / step) * step).toFixed(0);
    }

    if (finalValue !== loanAmountValue) {
      onAmountChange?.({ [FormFieldNames.netTotal]: finalValue });
    }

    setIsEditing(false);
  };

  const handleValueChange = (values: { floatValue?: number }) => {
    const newValue = values.floatValue === undefined ? 0 : values.floatValue;

    setValue(FormFieldNames.netTotal, newValue);

    debouncedOnAmountChange(newValue);
  };

  return (
    <div className="flex justify-between items-center">
      <div className="w-full flex justify-between items-center mr-2">
        <Typography variant="text-l" affects="semibold">
          {t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.amountInputLabel)}
        </Typography>

        {isEditing ? (
          <NumericFormat
            inputMode="decimal"
            getInputRef={inputRef}
            className={cn(
              'w-full text-right bg-transparent border-none outline-none text-base',
              'text-[1.5rem] leading-8 tracking-[-.06rem] font-bold',
              errors[FormFieldNames.netTotal] && 'text-red-500',
            )}
            allowLeadingZeros={false}
            allowNegative={false}
            fixedDecimalScale={false}
            onValueChange={handleValueChange}
            suffix={` ${Currencies.euro}`}
            onBlur={handleBlur}
            thousandSeparator=" "
            value={loanAmountValue || ''}
            isAllowed={(values) => {
              const { formattedValue } = values;
              if (formattedValue.startsWith('0')) {
                return false;
              }
              return true;
            }}
          />
        ) : (
          <Typography variant="text-2xl" affects="bold">
            {`${loanAmountValue?.toLocaleString('fr-FR')} ${Currencies.euro}`}
          </Typography>
        )}
      </div>

      <button
        className="p-2 rounded-lg bg-zinc-100"
        onClick={handleEditClick}
        type="button"
      >
        <PencilIcon className="w-4 h-4" />
      </button>
    </div>
  );
};
