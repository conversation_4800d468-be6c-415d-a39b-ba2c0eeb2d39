import {
  Currencies,
  FormFieldNames,
  LocalStorageKeys,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Notification } from 'components/notification';
import { Typography } from 'components/typography';
import { useRootContext } from 'context/root';
import { useUpdateApplicationCreditInfo } from 'hooks';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import SmearIcon from 'icons/smear.svg?react';
import { lazy, Suspense, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getUpdateApplicationCreditInfoVariables } from 'services';
import {
  getSmallLoanAmountFromStorage,
  removeFromStorage,
} from 'services/storage-service';
import { formatNumber } from 'utils';

import type { LoanAmountFormType } from './schema';

const LoanAmountDecreaseModalLazy = lazy(() =>
  import('./LoanAmountDecreaseModal').then((mod) => ({
    default: mod.LoanAmountDecreaseModal,
  })),
);

export const ApprovedLoanAmountBlock = () => {
  const { t: ts } = useTranslation(LocizeNamespaces.signing);
  const { updateApplicationCreditInfo } = useUpdateApplicationCreditInfo();
  const { quietUserRefetch, user } = useRootContext();
  const { application, applicationReferenceKey, quietApplicationRefetch } =
    useGetCurrentApplicationSuspense();

  // const [isUpdatingLoanAmount, setIsUpdatingLoanAmount] = useState(false);

  if (!application?.id) {
    throw new Error(
      'Application is not found while rendering approved loan amount block',
    );
  }

  const initialLoanAmount = getSmallLoanAmountFromStorage(application.id);

  const requestedLoanAmount = +application.requested_amount;

  const isInitialLoanAmountDisallowed = requestedLoanAmount < initialLoanAmount;

  const [isOpenDialog, setIsOpenDialog] = useState(false);

  const updateLoanAmount = async (amount: number) => {
    // setIsUpdatingLoanAmount(true);
    try {
      await updateApplicationCreditInfo(
        getUpdateApplicationCreditInfoVariables({
          application,
          applicationReferenceKey,
          netTotal: amount,
          periodMonths: application.credit_info?.period_months || 1,
          downPayment: application.credit_info?.down_payment || 0,
        }),
      );

      await quietApplicationRefetch();
      await quietUserRefetch();

      removeFromStorage(LocalStorageKeys.creditLineChosenAmount);
    } catch (error) {
      console.error(error);
    } finally {
      // setIsUpdatingLoanAmount(false);
    }
  };

  const onConfirm = async (values: LoanAmountFormType) => {
    try {
      await updateLoanAmount(values[FormFieldNames.netTotal]);
    } catch (error) {
      console.error(error);
    } finally {
      setIsOpenDialog(false);
    }
  };

  return (
    <div className="flex flex-col items-center py-8">
      <Typography variant="s" className="text-center">
        {ts(LOCIZE_SIGNING_TRANSLATION_KEYS.approvedTitle, {
          userName: user?.profile?.first_name,
        })}
      </Typography>

      <Typography className="mt-4" variant="text-l">
        {ts(LOCIZE_SIGNING_TRANSLATION_KEYS.approvedLoanDescription)}
      </Typography>

      {isInitialLoanAmountDisallowed && (
        <Notification className="max-w-[400px] mt-6">
          {ts(LOCIZE_SIGNING_TRANSLATION_KEYS.moreThanMaxLoanDisclaimer)}
        </Notification>
      )}

      <div className="flex flex-col items-center justify-center w-full relative mt-8 pb-4">
        <Typography className="relative z-10 " variant="xl">
          {formatNumber({
            value: requestedLoanAmount,
          })}{' '}
          {Currencies.euro}
        </Typography>

        <SmearIcon className="w-full absolute -bottom-1 left-0 " />
      </div>

      {/* <div>
        {selectedAmount === maxLoanAmount ? (
          <Typography
            variant="text-s"
            affects="link"
            className="mt-8 cursor-pointer hover:text-primary-brand-02"
            onClick={() => setIsOpenDialog(true)}
          >
            {ts(LOCIZE_SIGNING_TRANSLATION_KEYS.decreaseLimitButtonLabel)}
          </Typography>
        ) : (
          <Notification className="mt-8">
            <AppLocalizationComponent
              components={{
                button: (
                  <div
                    onClick={() => {
                      if (isUpdatingLoanAmount) {
                        return;
                      }
                      updateLoanAmount(maxLoanAmount);
                    }}
                    className={cn(
                      'text-primary-brand-02 underline underline-from-font hover:text-primary-brand-02 inline cursor-pointer hover:opacity-80',
                      isUpdatingLoanAmount &&
                        'cursor-progress text-neutral-200 no-underline hover:text-neutral-200 hover:opacity-100',
                    )}
                  />
                ),
              }}
              values={{
                loanAmount: formatNumber({
                  value: maxLoanAmount,
                }),
              }}
              locizeKey={
                LOCIZE_SIGNING_TRANSLATION_KEYS.increaseLoanAmountNotificationText
              }
              t={ts}
            />
          </Notification>
        )}
      </div> */}

      <Suspense fallback={null}>
        <LoanAmountDecreaseModalLazy
          onConfirm={onConfirm}
          currentAmount={requestedLoanAmount}
          isOpen={isOpenDialog}
          onOpenChange={setIsOpenDialog}
        />
      </Suspense>
    </div>
  );
};
