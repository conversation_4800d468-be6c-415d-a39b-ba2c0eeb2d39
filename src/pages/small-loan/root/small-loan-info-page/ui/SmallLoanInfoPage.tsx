import { useIsMobileView } from 'hooks/system';
import LoanInfo from 'img/small-loan-info.webp';
import { lazy, Suspense } from 'react';
import Skeleton from 'react-loading-skeleton';

import { SMALL_LOAN_INFO_PAGE_FEATURES } from '../config';
import { SmallLoanInfoCard } from './SmallLoanInfoCard';
import { SmallLoanInfoCarousel } from './SmallLoanInfoCarousel';

const SmallLoanOfferBanner = lazy(() =>
  import('./SmallLoanOfferBanner').then((module) => ({
    default: module.SmallLoanOfferBanner,
  })),
);

export const SmallLoanInfoPage = () => {
  const isMobileView = useIsMobileView();

  return (
    <div className={'bg-black  h-full w-full flex flex-col'}>
      {!isMobileView && (
        <div className=" w-full h-1/2 ">
          <img
            src={LoanInfo}
            alt="Small loan info"
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {!isMobileView && (
        <Suspense
          fallback={<Skeleton className="w-full h-[2.5rem] rounded-none!" />}
        >
          <div className="animate-in">
            <SmallLoanOfferBanner />
          </div>
        </Suspense>
      )}

      {isMobileView ? (
        <SmallLoanInfoCarousel />
      ) : (
        <div className="bg-stone-900 text-white h-1/2 md:p-6 lg:p-12 overflow-y-auto">
          <div className="grid grid-cols-2 gap-6  h-full">
            {SMALL_LOAN_INFO_PAGE_FEATURES.map((feature, index) => {
              const { icon, title, description } = feature;
              return (
                <SmallLoanInfoCard
                  key={index}
                  icon={icon}
                  title={title}
                  description={description}
                />
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
