import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from 'components/ui/carousel';

import { SMALL_LOAN_INFO_PAGE_FEATURES } from '../config';
import { SmallLoanInfoCard } from './SmallLoanInfoCard';
import { SmallLoanOfferBanner } from './SmallLoanOfferBanner';

export const SmallLoanInfoCarousel = () => {
  return (
    <>
      <SmallLoanOfferBanner />
      <div className="w-full px-6 pt-8 pb-16 bg-black -mb-8">
        <Carousel
          className="w-full [&>div:first-child]:h-auto"
          opts={{ align: 'start', dragFree: true }}
        >
          <CarouselContent isInteractive>
            {SMALL_LOAN_INFO_PAGE_FEATURES.map(
              ({ icon, title, description }, index) => (
                <CarouselItem className="basis-[16.625rem]" key={index}>
                  <SmallLoanInfoCard
                    icon={icon}
                    title={title}
                    description={description}
                  />
                </CarouselItem>
              ),
            )}
          </CarouselContent>
        </Carousel>
      </div>
    </>
  );
};
