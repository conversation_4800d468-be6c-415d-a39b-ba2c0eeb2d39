import { SmallLoanProgressBar } from 'components/progress-bar/small-loan-progress-bar/SmallLoanProgressBar';
import { cn } from 'utils/tailwind';

import { SmallLoanTitle } from './SmallLoanTitle';

type SmallLoanHeaderProps = {
  className?: string;
};

export const SmallLoanHeader = ({ className }: SmallLoanHeaderProps) => {
  return (
    <div className={cn(className, 'w-full')}>
      <SmallLoanTitle className="mb-4 text-[1.875rem] w-full max-w-100 flex flex-col" />
      <SmallLoanProgressBar className="mb-4" />
    </div>
  );
};

export default SmallLoanHeader;
