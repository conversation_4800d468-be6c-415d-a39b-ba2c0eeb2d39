import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SUCCESS_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { useRootContext } from 'context/root';
import { useSuccessPageLogic } from 'hooks/page-logic/small-loan';
import SuccessIcon from 'icons/result-screen-icon.svg?react';
import { Trans, useTranslation } from 'react-i18next';
import { formatNumber } from 'utils';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

import { NewsletterAgreementSection } from './NewsletterAgreementSection';

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.success);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const { user } = useRootContext();

  const {
    application,
    visiblePageAttributes,
    everyPayProviderEnabled,
    onEstoAccountButtonClick,
    onCustomerProfileRedirectionButtonClick,
    isInstantPayout,
  } = useSuccessPageLogic();

  const locizeSuccessfulDisclaimerKey = (() => {
    if (isInstantPayout) {
      return LOCIZE_SUCCESS_TRANSLATION_KEYS.successSignedContractDisclaimer;
    }

    return LOCIZE_SUCCESS_TRANSLATION_KEYS.successSignedContractDisclaimerRegular;
  })();

  return (
    <FullScreenLayout>
      <SuccessIcon />
      <Typography className="mt-6 text-center" variant="m">
        {t(LOCIZE_SUCCESS_TRANSLATION_KEYS.successLabel)}
      </Typography>
      <Typography variant="text-l" className="mt-4 text-center">
        {
          <Trans
            t={t}
            i18nKey={locizeSuccessfulDisclaimerKey}
            values={{
              loanAmount: formatNumber({
                value: application.credit_info?.net_total,
              }),
            }}
            components={{
              strong: <span className="font-bold" />,
            }}
          />
        }
      </Typography>

      {!user?.newsletter_agreement ? <NewsletterAgreementSection /> : null}

      <div className="flex flex-col mt-10 w-full">
        {visiblePageAttributes[PageAttributeNames.estoAccountSection] ? (
          <Button fullWidth onClick={onEstoAccountButtonClick} variant="grey">
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.estoAccount)}
          </Button>
        ) : null}
        {everyPayProviderEnabled &&
        visiblePageAttributes[PageAttributeNames.standingOrderSection] ? (
          <Button
            fullWidth
            onClick={onCustomerProfileRedirectionButtonClick}
            variant="grey"
            className="mt-4"
          >
            {t(LOCIZE_SUCCESS_TRANSLATION_KEYS.signStandingPayment)}
          </Button>
        ) : null}
      </div>
    </FullScreenLayout>
  );
};

export default Page;
