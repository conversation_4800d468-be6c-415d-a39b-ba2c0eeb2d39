import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LOCIZE_SUCCESS_TRANSLATION_KEYS,
  LocizeNamespaces,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Card } from 'components/ui/card';
import { useRootContext } from 'context/root';
import { useRudderStack, useUpdateUserTerms } from 'hooks';
import CheckIcon from 'icons/check-system-blue.svg?react';
import MailIcon from 'icons/mail.svg?react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

const agreementFeature = [
  LOCIZE_SUCCESS_TRANSLATION_KEYS.newsletterAgreementFeature,
  LOCIZE_SUCCESS_TRANSLATION_KEYS.newsletterAgreementFeature2,
  LOCIZE_SUCCESS_TRANSLATION_KEYS.newsletterAgreementFeature3,
];

export const NewsletterAgreementSection = () => {
  const { ruderStackEvents } = useRudderStack();
  const [isVisible, setIsVisible] = useState(true);
  const { t } = useTranslation(LocizeNamespaces.success);
  const { t: te } = useTranslation(LocizeNamespaces.errors);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const { user } = useRootContext();

  const { updateUserTerms, userTermsUpdating } = useUpdateUserTerms();

  const onConfirmSubscriptionButtonClick = async () => {
    if (!user) {
      throw new Error('User is not defined');
    }

    try {
      const result = await updateUserTerms({
        user_id: user.id,
        newsletter_agreement: true,
        conditions_agreement: true,
        allow_pension_query: user.allow_pension_query,
      });

      if (!result.data?.update_user_terms) {
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        return;
      }

      setIsVisible(false);

      toast.success(
        tc(LOCIZE_COMMON_TRANSLATION_KEYS.newsletterAgreementSuccess),
      );

      ruderStackEvents.marketingConsentOnSuccess({ userId: user.id });
    } catch (error) {
      console.warn('Error during updating user terms', error);
    }
  };

  return (
    <div
      className={`transition-all duration-700 ease-out overflow-hidden ${
        isVisible
          ? 'opacity-100 scale-100 max-h-[31.25rem] mt-6'
          : 'opacity-0 scale-90 max-h-0 mt-0'
      }`}
    >
      <Card className="p-6 border-neutral-200">
        <div className="flex  flex-col gap-[1.125rem]">
          <MailIcon />
          <Typography variant="xxs" affects="bold">
            {t(LOCIZE_SUCCESS_TRANSLATION_KEYS.newsletterAgreementTitle3)}
          </Typography>
        </div>
        <div className="flex flex-col gap-1 pt-[1.125rem]">
          {agreementFeature.map((feature) => (
            <div className="flex items-center gap-2" key={feature}>
              <CheckIcon />
              <Typography variant="text-s">{t(feature)}</Typography>
            </div>
          ))}
        </div>
        <div>
          <Button
            loading={userTermsUpdating}
            className="my-6"
            fullWidth
            onClick={onConfirmSubscriptionButtonClick}
            variant="blue"
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.newsletterApplyButton)}
          </Button>
        </div>
        <div>
          <Typography
            variant="text-xs"
            tag="div"
            className="text-gray-500 text-center"
          >
            <AppLocalizationComponent
              locizeKey={
                LOCIZE_SUCCESS_TRANSLATION_KEYS.newsletterAgreementDisclaimer
              }
              components={{
                privacyLink: (
                  <AppExternalLink
                    className="underline underline-offset-2 hover:text-primary-brand-02"
                    to={REDIRECT_URLS.privacyPolicy}
                  />
                ),
              }}
              t={t}
            />
          </Typography>
        </div>
      </Card>
    </div>
  );
};
