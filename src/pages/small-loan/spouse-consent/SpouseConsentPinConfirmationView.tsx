import PinConfirmationComponent from 'components/pin-confirmation/PinConfirmation';
import { useSpouseConsentPageContext } from 'context/small-loan';
import { useEffectOnce } from 'hooks';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

const isIos = !!navigator.userAgent.match(/iPhone|iPad|iPod/i);

let isSigning = false;
let isExecuted = false;

export const SpouseConsentPinConfirmationView = () => {
  const {
    smartIdSpouseSignaturePollChallengeId,
    mobileIdSpouseSignaturePollChallengeId,
    onPinConfirmationCancel,
    signAppByMobileIdOrSmartId,
  } = useSpouseConsentPageContext();

  useEffectOnce(() => {
    const handleFocus = () => resignOnIosDeviceFocus();
    window.addEventListener('focus', handleFocus);

    const timeoutId = setTimeout(() => {
      signContract();
    }, 3000);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('focus', handleFocus);
    };
  });

  const signContract = () => {
    if (isExecuted || isSigning) {
      return;
    }

    isSigning = true;

    signAppByMobileIdOrSmartId().finally(() => {
      isSigning = false;
      isExecuted = true;
    });
  };

  const resignOnIosDeviceFocus = () => {
    if (isIos) {
      signContract();
    }
  };

  return (
    <FullScreenLayout>
      <PinConfirmationComponent
        onCancel={onPinConfirmationCancel}
        pin={
          smartIdSpouseSignaturePollChallengeId ||
          mobileIdSpouseSignaturePollChallengeId
        }
        isCancelButtonVisible={!isExecuted && !isSigning}
      />
    </FullScreenLayout>
  );
};
