import { SpouseConsentPageViewTypes } from 'app-constants';
import { DefaultLoader } from 'components/default-loader/DefaultLoader';
import { useSpouseConsentPageContext } from 'context/small-loan';
import { SpouseConsentPageLogicProvider } from 'context/small-loan/SpouseConsentPageLogicProvider';
import { lazy, Suspense } from 'react';

const SpouseConsentPendingView = lazy(() =>
  import('../../../components/default-loader/DefaultLoader').then((module) => ({
    default: module.DefaultLoader,
  })),
);

const SpouseConsentPinConfirmationView = lazy(() =>
  import('./SpouseConsentPinConfirmationView').then((module) => ({
    default: module.SpouseConsentPinConfirmationView,
  })),
);

const SpouseConsentSigningView = lazy(() =>
  import('./SpouseConsentSigningView').then((module) => ({
    default: module.SpouseConsentSigningView,
  })),
);

const SpouseConsentSuccessView = lazy(() =>
  import('./SpouseConsentSuccessView').then((module) => ({
    default: module.SpouseConsentSuccessView,
  })),
);

const SpouseConsentSigningContractView = lazy(() =>
  import('./SpouseConsentSigningContractView').then((module) => ({
    default: module.SigningContractView,
  })),
);

const SpouseConsentFailureView = lazy(() =>
  import('./SpouseConsentFailureView').then((module) => ({
    default: module.SpouseConsentFailureView,
  })),
);

const Page = () => {
  const { spouseConsentPageViewType } = useSpouseConsentPageContext();

  const renderSigningPageContent = () => {
    switch (spouseConsentPageViewType) {
      case SpouseConsentPageViewTypes.preparing:
        return <SpouseConsentSigningContractView />;
      case SpouseConsentPageViewTypes.signingContract:
        return <SpouseConsentSigningContractView />;
      case SpouseConsentPageViewTypes.pinConfirmation:
        return <SpouseConsentPinConfirmationView />;
      case SpouseConsentPageViewTypes.signing:
        return <SpouseConsentSigningView />;
      case SpouseConsentPageViewTypes.pending:
        return <SpouseConsentPendingView />;
      case SpouseConsentPageViewTypes.success:
        return <SpouseConsentSuccessView />;
      case SpouseConsentPageViewTypes.failure:
        return <SpouseConsentFailureView />;
      default:
        return <SpouseConsentSigningView />;
    }
  };

  return <div>{renderSigningPageContent()}</div>;
};

const SpouseConsentPage = () => (
  <Suspense fallback={<DefaultLoader />}>
    <SpouseConsentPageLogicProvider>
      <Page />
    </SpouseConsentPageLogicProvider>
  </Suspense>
);

export default SpouseConsentPage;
