import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Typography } from 'components/typography';
import SuccessIcon from 'icons/success.svg?react';
import { useTranslation } from 'react-i18next';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

export const SpouseConsentSuccessView = () => {
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  return (
    <FullScreenLayout>
      <SuccessIcon />
      <Typography className="mt-6 text-center" variant="m">
        {`${tc(LOCIZE_COMMON_TRANSLATION_KEYS.successLabel)}!`}
      </Typography>
      <Typography className="mt-4 text-center">
        {tc(LOCIZE_COMMON_TRANSLATION_KEYS.appInProcessLabel)}
      </Typography>
    </FullScreenLayout>
  );
};
