import { Separator } from '@radix-ui/react-dropdown-menu';
import {
  AppOnboardingCampaigns,
  AppRegions,
  AppSearchParams,
  Currencies,
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_CREDIT_LINE_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
  MONTHS_IN_YEAR,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { CampaignBanner } from 'components/campaign-banner';
import { FormInputField } from 'components/form/form-input-field';
import { LogoutButton } from 'components/logout-button';
import { Notification } from 'components/notification';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from 'components/ui/card';
import { Form } from 'components/ui/form';
import { useSigningPageContext } from 'context/credit-line';
import { useRootContext } from 'context/root';
import { region } from 'environment';
import type { SigningFormType } from 'hooks/page-logic/credit-line';
import { useGetPageUrl } from 'hooks/use-get-page-url';
import type { AnyObject } from 'models';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { roundNumberUpByTwoDecimals } from 'services/math-service';

export const SigningView = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.signing);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const { t: tcl } = useTranslation(LocizeNamespaces.creditLine);
  const [searchParams] = useSearchParams();
  const { user } = useRootContext();

  const {
    contractLink,
    userCanSignContract,
    processingSigningPage,
    form,
    onSigningFormSubmit,
    userInfoValidationErrors,
  } = useSigningPageContext();

  const {
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing: pageUrlAndNavigationProcessingBackButton,
  } = useGetPageUrl();

  const userCreditAccount = user?.credit_accounts?.[0];

  const isInterestFreeOnboarding =
    searchParams.get(AppSearchParams.onboarding) ===
    AppOnboardingCampaigns.creditLineInterestFree;

  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = () => {
    setIsDownloading(true);
    window.location.href = contractLink;

    setTimeout(() => {
      setIsDownloading(false);
    }, 2000);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSigningFormSubmit)}
        className="grid w-full gap-2"
      >
        <Card className="w-full max-w-md rounded-3xl shadow-none border border-solid border-transparent md:border-neutral-200 md:shadow-container">
          <CardHeader className="p-0 md:p-6 md:pb-0">
            <div className="flex justify-between items-center">
              <Typography variant="xxs">
                {t(LOCIZE_SIGNING_TRANSLATION_KEYS.contractTitle)}
              </Typography>

              <Button
                variant="transparent"
                className="hover:bg-transparent p-0"
                onClick={handleDownload}
                loading={isDownloading}
                disabled={processingSigningPage}
              >
                <Typography
                  variant="text-s"
                  affects="link"
                  className="font-normal hover:text-primary-brand-02"
                >
                  {t(LOCIZE_SIGNING_TRANSLATION_KEYS.viewTerms)}
                </Typography>
              </Button>
            </div>
          </CardHeader>

          <CardContent className="mt-6 p-0 md:p-6 md:pt-0">
            {isInterestFreeOnboarding ? (
              <div className="mt-4">
                <Separator className="h-px w-full bg-neutral-200" />

                <Typography variant="text-s" className="mt-4 text-neutral-500">
                  {t(LOCIZE_SIGNING_TRANSLATION_KEYS.campaignOfferTitle)}
                </Typography>

                <CampaignBanner
                  className="mt-2"
                  title={tcl(
                    LOCIZE_CREDIT_LINE_TRANSLATION_KEYS.campaignBannerTitle,
                  )}
                  info={''}
                />
              </div>
            ) : null}

            <div>
              <Separator className="h-px w-full bg-neutral-200" />
              <div className="flex justify-between items-center py-4">
                <Typography className="text-neutral-500" variant="text-s">
                  {tc(LOCIZE_COMMON_TRANSLATION_KEYS.signingFeeTitle)}
                </Typography>

                <Typography variant="text-s">{`0 ${Currencies.euro}`}</Typography>
              </div>
            </div>

            {userCreditAccount?.management_fee_monthly === 0 &&
            region === AppRegions.et ? (
              <div>
                <Separator className="h-px w-full bg-neutral-200" />

                <div className="flex justify-between items-center py-4">
                  <Typography className="text-neutral-500" variant="text-s">
                    {tc(LOCIZE_COMMON_TRANSLATION_KEYS.managementFeeTitle)}
                  </Typography>

                  <Typography variant="text-s">{`${userCreditAccount.management_fee_monthly} ${Currencies.euro}`}</Typography>
                </div>
              </div>
            ) : null}

            <div>
              <Separator className="h-px w-full bg-neutral-200" />

              <div className="flex justify-between items-center py-4">
                <Typography className="text-neutral-500" variant="text-s">
                  {tc(LOCIZE_COMMON_TRANSLATION_KEYS.monthlyInterestTitle)}
                </Typography>

                <Typography variant="text-s">{`${roundNumberUpByTwoDecimals(
                  (userCreditAccount?.annual_pct_rate ?? 0) / MONTHS_IN_YEAR,
                )}%`}</Typography>
              </div>

              <Separator className="h-px w-full bg-neutral-200" />
            </div>

            <FormInputField<SigningFormType>
              className="mt-6"
              name={FormFieldNames.iban}
              control={form.control}
              label={t(LOCIZE_SIGNING_TRANSLATION_KEYS.ibanFieldLabel)}
              invalid={
                userInfoValidationErrors[FormFieldNames.iban] ||
                !!form.getFieldState(FormFieldNames.iban).error
              }
              info={t(LOCIZE_SIGNING_TRANSLATION_KEYS.ibanTooltipLabel)}
              disabled={processingSigningPage}
            />

            <div className="mt-4">
              <Typography
                className="text-center text-neutral-500"
                variant="text-s"
                tag="div"
              >
                <AppLocalizationComponent
                  components={{
                    site_link: (
                      <AppExternalLink
                        className="underline hover:text-primary-brand-02"
                        to={
                          (REDIRECT_URLS.termsPageUrs as AnyObject)[
                            i18n.language
                          ]
                        }
                      />
                    ),
                    contract_link: (
                      <AppExternalLink
                        className="underline hover:text-primary-brand-02"
                        openInNewTab={false}
                        to={contractLink}
                      />
                    ),
                  }}
                  locizeKey={
                    LOCIZE_SIGNING_TRANSLATION_KEYS.acceptTermsOfContract
                  }
                  t={t}
                />
              </Typography>
            </div>
          </CardContent>

          <CardFooter className="flex-col gap-6 pt-6 px-0 pb-0 md:p-6 md:pt-0">
            {userCanSignContract ? (
              <Button
                fullWidth
                variant="black"
                disabled={pageUrlAndNavigationProcessingBackButton}
                loading={processingSigningPage}
                type="submit"
              >
                {t(LOCIZE_SIGNING_TRANSLATION_KEYS.signContract)}
              </Button>
            ) : (
              <>
                <Notification>
                  {t(
                    LOCIZE_SIGNING_TRANSLATION_KEYS.changeLoginMethodDisclaimer,
                  )}
                </Notification>

                <LogoutButton variant="black" fullWidth>
                  {t(
                    LOCIZE_SIGNING_TRANSLATION_KEYS.changeLoginMethodButtonLabel,
                  )}
                </LogoutButton>
              </>
            )}
          </CardFooter>
        </Card>

        <Button
          loading={pageUrlAndNavigationProcessingBackButton}
          fullWidth
          variant="white"
          className="mt-2"
          onClick={() => {
            getPageUrlAndNavigate(false);
          }}
          disabled={processingSigningPage}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
      </form>
    </Form>
  );
};
