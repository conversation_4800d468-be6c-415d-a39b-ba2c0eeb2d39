import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { useSigningPageContext } from 'context/credit-line';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

export const BanklinkSigningView = () => {
  const { banklinkOptions, signAppWithBanklink, processingSigningPage } =
    useSigningPageContext();
  const { t } = useTranslation(LocizeNamespaces.signing);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const [selectedBanklink, setSelectedBanklink] = useState<string | null>(null);

  return (
    <FullScreenLayout>
      <div className="flex flex-col gap-4 w-full items-center">
        <Typography variant="xs" className="mt-4">
          {t(LOCIZE_SIGNING_TRANSLATION_KEYS.chooseBankLabel)}
        </Typography>

        <div className="mt-8 grid grid-cols-[repeat(auto-fill,minmax(8rem,1fr))] gap-2 w-full">
          {banklinkOptions.map((option) =>
            option ? (
              <Button
                asChild
                key={option.key}
                onClick={() => setSelectedBanklink(option.key)}
                className={cn(
                  'cursor-pointer hover:bg-gray-50',
                  selectedBanklink === option.key &&
                    'cursor-not-allowed hover:bg-transparent',
                )}
              >
                <div
                  className={cn(
                    'h-12 px-[18px] w-full max-w-[10.2rem] bg-white rounded-[14px] shadow-[0px_3px_10px_0px_rgba(42,40,135,0.05)] border border-solid border-gray-200 justify-center items-center gap-2 inline-flex overflow-hidden',
                    selectedBanklink === option.key && 'border-primary-black',
                  )}
                >
                  <img
                    alt={option.title}
                    className="w-[87px] h-6"
                    src={option.logo_url}
                  />
                </div>
              </Button>
            ) : null,
          )}
        </div>

        <Button
          className="mt-10"
          fullWidth
          variant="black"
          disabled={!!processingSigningPage || !selectedBanklink}
          loading={!!processingSigningPage}
          onClick={() => {
            if (selectedBanklink) {
              signAppWithBanklink?.(selectedBanklink);
            }
          }}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>
      </div>
    </FullScreenLayout>
  );
};
