import PinConfirmationComponent from 'components/pin-confirmation/PinConfirmation';
import { useSigningPageContext } from 'context/credit-line';
import { useEffectOnce, useIsMobileDevice } from 'hooks';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

let isSigning = false;
let isExecuted = false;

export const PinConfirmationView = () => {
  const {
    smartIdContractSignaturePollChallengeId,
    mobileIdContractSignaturePollChallengeId,
    onPinConfirmationCancel,
    signAppByMobileIdOrSmartId,
  } = useSigningPageContext();
  const isMobile = useIsMobileDevice();

  useEffectOnce(() => {
    const handleFocus = () => resignOnMobileDeviceFocus();
    window.addEventListener('focus', handleFocus);

    const timeoutId = setTimeout(() => {
      signContract();
    }, 3000);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('focus', handleFocus);
    };
  });

  const signContract = () => {
    if (isExecuted || isSigning) {
      return;
    }

    isSigning = true;

    signAppByMobileIdOrSmartId()
      .then(() => {
        isExecuted = true;
      })
      .finally(() => {
        isSigning = false;
      });
  };

  const resignOnMobileDeviceFocus = () => {
    if (isMobile) {
      signContract();
    }
  };

  return (
    <FullScreenLayout>
      <PinConfirmationComponent
        onCancel={onPinConfirmationCancel}
        pin={
          smartIdContractSignaturePollChallengeId ||
          mobileIdContractSignaturePollChallengeId
        }
        isCancelButtonVisible={!isExecuted && !isSigning}
      />
    </FullScreenLayout>
  );
};
