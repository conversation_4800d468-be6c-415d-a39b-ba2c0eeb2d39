import { ContactForm } from 'components/contact-form';
import {
  type ContactFormType,
  useContactPageLogic,
} from 'hooks/page-logic/credit-line';

const Contact = () => {
  const {
    form,
    onContactFormSubmit,
    politicalExposureOptions,
    visiblePageAttributes,
    userInfoValidationErrors,
    conditionsAgreement,
  } = useContactPageLogic();

  const contextValue = {
    visiblePageAttributes,
    userInfoValidationErrors,
    politicalExposureOptions,
    showPoliticalExposureInfo: true,
    linkClassName: 'underline cursor-pointer hover:text-primary-brand-02',
  };

  return (
    <ContactForm<ContactFormType>
      form={form}
      onSubmit={onContactFormSubmit}
      contextValue={contextValue}
      passFormToSubmit={false}
      conditionsAgreement={conditionsAgreement}
    />
  );
};

export default Contact;
