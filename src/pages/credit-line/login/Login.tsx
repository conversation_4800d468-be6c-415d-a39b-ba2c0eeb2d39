import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LoginPageViewTypes,
} from 'app-constants';
import { AppContactUs } from 'components';
import { useLoginPageContext } from 'context/credit-line';
import { LoginPageLogicProvider } from 'context/credit-line/LoginPageLogicProvider';

import { IdCardView } from './IdCardView';
import { LoginView } from './LoginView';
import { MagicLinkView } from './MagicLinkView';
import { PendingView } from './PendingView';
import { PinConfirmationView } from './PinConfirmationView';

const Page = () => {
  const { pageViewType } = useLoginPageContext();

  const renderLoginPageContent = () => {
    switch (pageViewType) {
      case LoginPageViewTypes.pinConfirmation:
        return <PinConfirmationView />;
      case LoginPageViewTypes.idCard:
        return <IdCardView />;
      case LoginPageViewTypes.magic:
        return <MagicLinkView />;
      case LoginPageViewTypes.pending:
        return <PendingView />;
      default:
        return <LoginView />;
    }
  };

  return (
    <div>
      {renderLoginPageContent()}
      <AppContactUs
        text={LOCIZE_COMMON_TRANSLATION_KEYS.contactInfoDisclaimer}
        visible={pageViewType === LoginPageViewTypes.login}
      />
    </div>
  );
};

const LoginPage = () => (
  <LoginPageLogicProvider>
    <Page />
  </LoginPageLogicProvider>
);

export default LoginPage;
