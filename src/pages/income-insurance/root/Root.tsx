import {
  AppSearchParams,
  CreditLineRoutePaths,
  IncomeInsuranceRoutePaths,
} from 'app-constants';
import { DefaultLoader } from 'components/default-loader/DefaultLoader';
import { ScrollIntoView } from 'components/scroll-into-view';
import { RootLogicProvider } from 'context/income-insurance/RootLogicProvider';
import { useRootContext } from 'context/root';
import { useCheckIsEqualCurrentPageUrl, usePageUrlSuspenseState } from 'hooks';
import { useIsMobileView } from 'hooks/system';
import { Suspense } from 'react';
import Skeleton from 'react-loading-skeleton';
import { Outlet, useLocation, useSearchParams } from 'react-router-dom';
import { cn } from 'utils/tailwind';
import { MainFooter } from 'widgets/footers/main-footer';
import { MainHeader } from 'widgets/headers/main-header';
import { ContainerLayout } from 'widgets/layouts/container-layout';
import { PageLayout } from 'widgets/layouts/page-layout/PageLayout';

import { InsuranceInfo } from './insurance-info/ui/InsuranceInfo';
import { InsuranceInstanceManager } from './InsuranceInstanceManager';

export const Root = () => {
  const { user } = useRootContext();
  const [searchParams] = useSearchParams();
  const insuranceHash = searchParams.get(AppSearchParams.insuranceHash);

  const { pathname } = useLocation();
  const isMobileView = useIsMobileView();
  const isLoginPath = pathname.includes(CreditLineRoutePaths.LOGIN);
  const isSigningUpPath = pathname.includes(CreditLineRoutePaths.SIGNING);
  const isStatusPath =
    pathname.includes(IncomeInsuranceRoutePaths.REJECT) ||
    pathname.includes(IncomeInsuranceRoutePaths.SUCCESS);

  const { loading, data } = usePageUrlSuspenseState();

  const isEqualCurrentPageUrl = useCheckIsEqualCurrentPageUrl();

  const renderLeftBlock = () => {
    //TODO: move back once we have resolved on the backend (|| (!user?.id && !insuranceHash))
    const isContentReady =
      (data?.page_url && !loading) ||
      isEqualCurrentPageUrl ||
      (!user?.id && !insuranceHash);

    if (!isContentReady) {
      return <LeftBlockSkeleton />;
    }

    return <InsuranceInfo />;
  };

  return (
    <RootLogicProvider>
      <PageLayout
        className={cn(
          'font-family-inter',
          isMobileView &&
            (isLoginPath || isSigningUpPath) &&
            '[&_#rightBody]:border-t [&_#rightBody]:border-solid [&_#rightBody]:border-neutral-200 [&_#rightBody]:rounded-t-xxl [&_#rightBody]:shadow-[0px_-5px_15px_0px_rgba(42,40,135,0.05)]',
        )}
        leftHeader={
          isMobileView ? null : (
            <ContainerLayout className="sticky top-0 z-10 bg-primary-white md:relative md:bg-transparent">
              <MainHeader className="bg-transparent" />
            </ContainerLayout>
          )
        }
        left={
          isStatusPath ? null : (
            <Suspense fallback={<LeftBlockSkeleton />}>
              {renderLeftBlock()}
            </Suspense>
          )
        }
        right={
          isStatusPath ? (
            <InsuranceInstanceManager>
              <Outlet />
            </InsuranceInstanceManager>
          ) : (
            <Suspense fallback={<DefaultLoader />}>
              <InsuranceInstanceManager>
                <ScrollIntoView componentId="rightDualPanel" />
                <Outlet />
              </InsuranceInstanceManager>
            </Suspense>
          )
        }
        leftFooter={
          isMobileView ? null : (
            <ContainerLayout>
              <MainFooter />
            </ContainerLayout>
          )
        }
      />
    </RootLogicProvider>
  );
};

function LeftBlockSkeleton() {
  return (
    <div className="w-screen h-108 flex flex-col gap-4 mt-4 mb-21 max-w-[18rem] md:mt-0 md:mb-0 md:max-w-88">
      <Skeleton className="w-full h-20 rounded-2xl" />
      <Skeleton className="w-full h-80 rounded-2xl" />
    </div>
  );
}
