import IdCardReaderPendingComponent from 'components/id-card-reader-pending-component/IdCardReaderPendingComponent';
import { useIncomeInsuranceLoginPageContext } from 'context/income-insurance';

export const IdCardView = () => {
  const { onIdCardLoginCancel, loginByIdCardProcessing } =
    useIncomeInsuranceLoginPageContext();

  const onCancel = () => {
    if (onIdCardLoginCancel) {
      onIdCardLoginCancel();
    }
  };

  return (
    <IdCardReaderPendingComponent
      onCancel={onCancel}
      isCancelButtonVisible={!loginByIdCardProcessing}
    />
  );
};
