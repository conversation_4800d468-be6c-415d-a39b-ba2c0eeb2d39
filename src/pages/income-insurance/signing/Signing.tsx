import {
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  SigningPageViewTypes,
} from 'app-constants';
import { AppLoader } from 'components';
import { useIncomeSigningPageContext } from 'context/income-insurance';
import { SigningPageLogicProvider } from 'context/income-insurance/SigningPageLogicProvider';
import { t } from 'i18next';
import { lazy } from 'react';

const PendingView = lazy(() =>
  import('../../../components/default-loader/DefaultLoader').then((module) => ({
    default: module.DefaultLoader,
  })),
);

const SigningView = lazy(() =>
  import('./SigningView').then((module) => ({
    default: module.SigningView,
  })),
);

const PinConfirmationView = lazy(() =>
  import('./PinConfirmationView').then((module) => ({
    default: module.PinConfirmationView,
  })),
);

const Page = () => {
  const {
    signingPageViewType,
    smartIdContractSignaturePreparationLoading,
    mobileIdContractSignaturePreparationLoading,
  } = useIncomeSigningPageContext();

  if (
    smartIdContractSignaturePreparationLoading ||
    mobileIdContractSignaturePreparationLoading
  ) {
    const delayedMessageText = smartIdContractSignaturePreparationLoading
      ? t(LOCIZE_SIGNING_TRANSLATION_KEYS.openSmartIdOnYourDevice)
      : t(LOCIZE_SIGNING_TRANSLATION_KEYS.openMobileIdOnYourDevice);

    return (
      <AppLoader
        isRelative
        delayedMessageTimeout={0}
        delayedMessageTitle={t(
          LOCIZE_SIGNING_TRANSLATION_KEYS.preparingSigningHeading,
        )}
        delayedMessageText={delayedMessageText}
      />
    );
  }

  const renderSigningPageContent = () => {
    switch (signingPageViewType) {
      case SigningPageViewTypes.pinConfirmation:
        return <PinConfirmationView />;
      case SigningPageViewTypes.signing:
        return <SigningView />;
      case SigningPageViewTypes.pending:
        return <PendingView />;
      default:
        return <SigningView />;
    }
  };

  return renderSigningPageContent();
};

const SigningPage = () => (
  <SigningPageLogicProvider>
    <Page />
  </SigningPageLogicProvider>
);

export default SigningPage;
