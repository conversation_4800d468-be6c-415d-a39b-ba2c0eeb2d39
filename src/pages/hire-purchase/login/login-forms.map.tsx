import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import {
  AppLanguages,
  AppLoginMethods,
  AppRegions,
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { FormInputField } from 'components/form/form-input-field';
import { FormPaymentMethodField } from 'components/form/form-payment-method-field';
import { FormPhoneField } from 'components/form/form-phone-field';
import { PrivacyPolicyDisclaimer } from 'components/privacy-policy-disclaimer';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { useLoginPageContext } from 'context/hire-purchase';
import { useRootContext } from 'context/root';
import { region } from 'environment';
import { type FieldValues, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { processGqlFormValidationErrors } from 'utils';
import * as z from 'zod';

import styles from './Login.module.scss';

const SmartIdAuthFormSchema = z.object({
  [FormFieldNames.pin]: z.string().min(1, {
    message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
  }),
});

type SmartIdAuthFormType = z.infer<typeof SmartIdAuthFormSchema>;

const SmartIdLoginForm = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.login);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const isLatvianRegionRussianSpecificCopy =
    region === AppRegions.lv && i18n.language === AppLanguages.ru;

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const {
    onLoginFormSubmit,
    loginValidationErrors,
    backNavigationDirectionValue,
  } = useLoginPageContext();

  const form = useForm<SmartIdAuthFormType>({
    resolver: zodResolver(SmartIdAuthFormSchema),
    defaultValues: {
      [FormFieldNames.pin]: '',
    },
  });

  const onSubmit = async (formFieldValues: FieldValues) => {
    try {
      await onLoginFormSubmit(formFieldValues);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid w-full gap-2"
      >
        <FormInputField<SmartIdAuthFormType>
          name={FormFieldNames.pin}
          control={form.control}
          label={t(
            isLatvianRegionRussianSpecificCopy
              ? LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabelLV
              : LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabel,
          )}
          invalid={
            loginValidationErrors[FormFieldNames.pin] ||
            !!form.getFieldState(FormFieldNames.pin).error
          }
          disabled={form.formState.isSubmitting}
        />

        <Button
          className="mt-12"
          disabled={pageUrlAndNavigationProcessing}
          loading={form.formState.isSubmitting}
          type="submit"
        >
          {t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
        </Button>
        <Button
          fullWidth
          className="mt-2"
          variant="white"
          loading={pageUrlAndNavigationProcessing}
          disabled={form.formState.isSubmitting}
          onClick={() => {
            getPageUrlAndNavigate(backNavigationDirectionValue);
          }}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
        <PrivacyPolicyDisclaimer />
      </form>
    </Form>
  );
};

const MobileIdFormSchema = z.object({
  [FormFieldNames.phone]: z.string().min(1, {
    message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
  }),
  [FormFieldNames.pin]: z.string().min(1, {
    message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
  }),
});

type MobileIdFormType = z.infer<typeof MobileIdFormSchema>;

const MobileIdLoginForm = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.login);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const isLatvianRegionRussianSpecificCopy =
    region === AppRegions.lv && i18n.language === AppLanguages.ru;

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const {
    onLoginFormSubmit,
    loginValidationErrors,
    backNavigationDirectionValue,
  } = useLoginPageContext();

  const form = useForm<MobileIdFormType>({
    resolver: zodResolver(MobileIdFormSchema),
    defaultValues: {
      [FormFieldNames.phone]: '',
      [FormFieldNames.pin]: '',
    },
  });

  const onSubmit = async (formFieldValues: FieldValues) => {
    try {
      await onLoginFormSubmit(formFieldValues);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid w-full gap-2"
      >
        <FormPhoneField<MobileIdFormType>
          name={FormFieldNames.phone}
          control={form.control}
          label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.phoneFieldLabel)}
          invalid={
            loginValidationErrors[FormFieldNames.phone] ||
            !!form.getFieldState(FormFieldNames.phone).error
          }
          disabled={form.formState.isSubmitting}
        />

        <FormInputField<MobileIdFormType>
          name={FormFieldNames.pin}
          control={form.control}
          label={t(
            isLatvianRegionRussianSpecificCopy
              ? LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabelLV
              : LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabel,
          )}
          invalid={
            loginValidationErrors[FormFieldNames.pin] ||
            !!form.getFieldState(FormFieldNames.pin).error
          }
          disabled={form.formState.isSubmitting}
        />

        <Button
          className="mt-12"
          disabled={pageUrlAndNavigationProcessing}
          loading={form.formState.isSubmitting}
          type="submit"
        >
          {t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
        </Button>
        <Button
          fullWidth
          className="mt-2"
          variant="white"
          loading={pageUrlAndNavigationProcessing}
          disabled={form.formState.isSubmitting}
          onClick={() => {
            getPageUrlAndNavigate(backNavigationDirectionValue);
          }}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
        <PrivacyPolicyDisclaimer />
      </form>
    </Form>
  );
};

const IdCardLoginForm = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const { onLoginFormSubmit, backNavigationDirectionValue } =
    useLoginPageContext();

  const form = useForm();

  const onSubmit = async (formFieldValues: FieldValues) => {
    try {
      await onLoginFormSubmit(formFieldValues);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  return (
    <>
      <p className={styles['id-card-disclaimer']}>
        {t(LOCIZE_LOGIN_TRANSLATION_KEYS.idCardLoginDisclaimer)}
      </p>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="grid w-full gap-2"
        >
          <Button
            className="mt-12"
            disabled={pageUrlAndNavigationProcessing}
            loading={form.formState.isSubmitting}
            type="submit"
          >
            {t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
          </Button>
          <Button
            fullWidth
            className="mt-2"
            variant="white"
            loading={pageUrlAndNavigationProcessing}
            disabled={form.formState.isSubmitting}
            onClick={() => {
              getPageUrlAndNavigate(backNavigationDirectionValue);
            }}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
          </Button>
          <PrivacyPolicyDisclaimer />
        </form>
      </Form>
    </>
  );
};

const PasswordLoginFormSchema = z.object({
  [FormFieldNames.username]: z.string().min(1, {
    message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
  }),
  [FormFieldNames.password]: z.string().min(1, {
    message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
  }),
});

type PasswordLoginFormType = z.infer<typeof PasswordLoginFormSchema>;

const PasswordLoginForm = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const {
    onLoginFormSubmit,
    loginValidationErrors,
    backNavigationDirectionValue,
  } = useLoginPageContext();

  const form = useForm<PasswordLoginFormType>({
    resolver: zodResolver(PasswordLoginFormSchema),
    defaultValues: {
      [FormFieldNames.username]: '',
      [FormFieldNames.password]: '',
    },
  });

  const onSubmit = async (formFieldValues: FieldValues) => {
    try {
      await onLoginFormSubmit(formFieldValues);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid w-full gap-2"
      >
        <FormInputField<PasswordLoginFormType>
          name={FormFieldNames.username}
          control={form.control}
          label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.userNameFieldLabel)}
          invalid={
            loginValidationErrors[FormFieldNames.username] ||
            !!form.getFieldState(FormFieldNames.username).error
          }
          disabled={form.formState.isSubmitting}
        />

        <FormInputField<PasswordLoginFormType>
          name={FormFieldNames.password}
          control={form.control}
          label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.passwordFieldLabel)}
          type="password"
          invalid={
            loginValidationErrors[FormFieldNames.password] ||
            !!form.getFieldState(FormFieldNames.password).error
          }
          disabled={form.formState.isSubmitting}
        />

        <Button
          className="mt-12"
          disabled={pageUrlAndNavigationProcessing}
          loading={form.formState.isSubmitting}
          type="submit"
        >
          {t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
        </Button>
        <Button
          fullWidth
          className="mt-2"
          variant="white"
          loading={pageUrlAndNavigationProcessing}
          disabled={form.formState.isSubmitting}
          onClick={() => {
            getPageUrlAndNavigate(backNavigationDirectionValue);
          }}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
        <PrivacyPolicyDisclaimer />
      </form>
    </Form>
  );
};

const BankLinkLoginFormSchema = z.object({
  [FormFieldNames.pin]: z.string().min(1, {
    message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
  }),
  [FormFieldNames.paymentMethodKey]: z.string().min(1, {
    message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
  }),
});

type BankLinkLoginFormType = z.infer<typeof BankLinkLoginFormSchema>;

const BankLinkLoginForm = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.login);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const isLatvianRegionRussianSpecificCopy =
    region === AppRegions.lv && i18n.language === AppLanguages.ru;

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const {
    onLoginFormSubmit,
    loginValidationErrors,
    banklinkOptions,
    backNavigationDirectionValue,
  } = useLoginPageContext();

  const form = useForm<BankLinkLoginFormType>({
    resolver: zodResolver(BankLinkLoginFormSchema),
    defaultValues: {
      [FormFieldNames.pin]: '',
      [FormFieldNames.paymentMethodKey]: '',
    },
  });

  const onSubmit = async (formFieldValues: FieldValues) => {
    try {
      await onLoginFormSubmit(formFieldValues);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  const selectedPaymentMethodKey = form.watch(FormFieldNames.paymentMethodKey);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid w-full gap-2"
      >
        <FormInputField<BankLinkLoginFormType>
          name={FormFieldNames.pin}
          control={form.control}
          label={t(
            isLatvianRegionRussianSpecificCopy
              ? LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabelLV
              : LOCIZE_LOGIN_TRANSLATION_KEYS.idCodeFieldLabel,
          )}
          invalid={
            loginValidationErrors[FormFieldNames.pin] ||
            !!form.getFieldState(FormFieldNames.pin).error
          }
          disabled={form.formState.isSubmitting}
        />

        <FormPaymentMethodField<BankLinkLoginFormType>
          containerClassName="mt-2.5"
          name={FormFieldNames.paymentMethodKey}
          control={form.control}
          options={banklinkOptions}
          selectedValue={selectedPaymentMethodKey}
          invalid={
            loginValidationErrors[FormFieldNames.paymentMethodKey] ||
            !!form.getFieldState(FormFieldNames.paymentMethodKey).error
          }
          disabled={form.formState.isSubmitting}
        />

        <Button
          className="mt-12"
          disabled={pageUrlAndNavigationProcessing}
          loading={form.formState.isSubmitting}
          type="submit"
        >
          {t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
        </Button>
        <Button
          fullWidth
          className="mt-2"
          variant="white"
          loading={pageUrlAndNavigationProcessing}
          disabled={form.formState.isSubmitting}
          onClick={() => {
            getPageUrlAndNavigate(backNavigationDirectionValue);
          }}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
        <PrivacyPolicyDisclaimer />
      </form>
    </Form>
  );
};

const EParakstsMobileLoginForm = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const { onLoginFormSubmit, backNavigationDirectionValue } =
    useLoginPageContext();

  const form = useForm();

  const onSubmit = async (formFieldValues: FieldValues) => {
    try {
      await onLoginFormSubmit(formFieldValues);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid w-full gap-2"
      >
        <Button
          className="mt-12"
          disabled={pageUrlAndNavigationProcessing}
          loading={form.formState.isSubmitting}
          type="submit"
        >
          {t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
        </Button>
        <Button
          fullWidth
          className="mt-2"
          variant="white"
          loading={pageUrlAndNavigationProcessing}
          disabled={form.formState.isSubmitting}
          onClick={() => {
            getPageUrlAndNavigate(backNavigationDirectionValue);
          }}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
        <PrivacyPolicyDisclaimer />
      </form>
    </Form>
  );
};

const EParakstsSmartCardLoginForm = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const { onLoginFormSubmit, backNavigationDirectionValue } =
    useLoginPageContext();

  const form = useForm();

  const onSubmit = async (formFieldValues: FieldValues) => {
    try {
      await onLoginFormSubmit(formFieldValues);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid w-full gap-2"
      >
        <Button
          className="mt-12"
          disabled={pageUrlAndNavigationProcessing}
          loading={form.formState.isSubmitting}
          type="submit"
        >
          {t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginButtonLabel)}
        </Button>
        <Button
          fullWidth
          className="mt-2"
          variant="white"
          loading={pageUrlAndNavigationProcessing}
          disabled={form.formState.isSubmitting}
          onClick={() => {
            getPageUrlAndNavigate(backNavigationDirectionValue);
          }}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
        <PrivacyPolicyDisclaimer />
      </form>
    </Form>
  );
};

export const LOGIN_FORMS_MAP = {
  [AppLoginMethods.mobileId]: MobileIdLoginForm,
  [AppLoginMethods.smartId]: SmartIdLoginForm,
  [AppLoginMethods.idCard]: IdCardLoginForm,
  [AppLoginMethods.password]: PasswordLoginForm,
  [AppLoginMethods.banklink]: BankLinkLoginForm,
  [AppLoginMethods.eParakstsMobile]: EParakstsMobileLoginForm,
  [AppLoginMethods.eParakstsSmartCard]: EParakstsSmartCardLoginForm,
};
