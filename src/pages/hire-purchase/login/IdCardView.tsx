import IdCardReaderPendingComponent from 'components/id-card-reader-pending-component/IdCardReaderPendingComponent';
import { useLoginPageContext } from 'context/hire-purchase';

export const IdCardView = () => {
  const { onIdCardLoginCancel, loginByIdCardProcessing } =
    useLoginPageContext();

  const onCancel = () => {
    if (onIdCardLoginCancel) {
      onIdCardLoginCancel();
    }
  };

  return (
    <IdCardReaderPendingComponent
      onCancel={onCancel}
      isCancelButtonVisible={!loginByIdCardProcessing}
    />
  );
};
