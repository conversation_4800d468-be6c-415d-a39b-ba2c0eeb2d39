import {
  AppLoginMethods,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { DisclaimerNotification } from 'components/notification';
import PinConfirmationComponent from 'components/pin-confirmation/PinConfirmation';
import { useLoginPageContext } from 'context/hire-purchase';
import { useTranslation } from 'react-i18next';

export const PinConfirmationView = () => {
  const {
    smartIdChallengeId,
    mobileIdChallengeId,
    stopLoginPolling,
    selectedLoginMethod,
    visiblePageAttributes,
  } = useLoginPageContext();

  const { t } = useTranslation(LocizeNamespaces.login);

  return (
    <>
      <PinConfirmationComponent
        onCancel={stopLoginPolling}
        pin={smartIdChallengeId || mobileIdChallengeId}
      />

      {visiblePageAttributes[PageAttributeNames.smartIdFullDisclaimer] &&
        selectedLoginMethod === AppLoginMethods.smartId && (
          <DisclaimerNotification className="mt-6">
            {t(LOCIZE_LOGIN_TRANSLATION_KEYS.smartIdFullVersionDisclaimer)}
          </DisclaimerNotification>
        )}
    </>
  );
};
