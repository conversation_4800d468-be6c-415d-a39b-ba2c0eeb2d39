import {
  ButtonVariants,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SUCCESS_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import c from 'clsx';
import {
  AppBold,
  AppButton,
  AppLoader,
  AppLocalizationComponent,
} from 'components';
import { useRootContext } from 'context/root';
import { useSuccessPageLogic } from 'hooks/page-logic/hire-purchase';
import { useTranslation } from 'react-i18next';

import { NewsLetterAgreementSection } from './NewsletterAgreementSection';
import styles from './Success.module.scss';
import { WithoutDownPaymentContent } from './WithoutDownPaymentContent';

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.success);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const { user } = useRootContext();
  const {
    processingSuccessPage,
    visiblePageAttributes,
    returnToMerchantButtonDisabled,
    onReturnToMerchantButtonClick,
    onCustomerProfileRedirectionButtonClick,
    onEstoAccountButtonClick,
    isWithDownPayment,
    userCreditAccount,
    shouldShowDisclaimerWithCreditAmount,
    everyPayProviderEnabled,
  } = useSuccessPageLogic();

  if (processingSuccessPage) {
    return <AppLoader isRelative />;
  }

  const hasCreditAccount = userCreditAccount?.signed_at;

  const shouldShowButtonsSection = !(
    !visiblePageAttributes[PageAttributeNames.standingOrderSection] &&
    !visiblePageAttributes[PageAttributeNames.returnToMerchantButton]
  );

  return (
    <div className={styles.container}>
      {isWithDownPayment ? (
        <p className={styles.paragraph}>
          {t(LOCIZE_SUCCESS_TRANSLATION_KEYS.afterMakingDownpaymentDisclaimer)}
        </p>
      ) : (
        <WithoutDownPaymentContent
          everyPayProviderEnabled={everyPayProviderEnabled}
        />
      )}

      {!user?.newsletter_agreement ? <NewsLetterAgreementSection /> : null}

      {shouldShowButtonsSection ? (
        <div className={c(styles.buttons, styles['buttons--hire-purchase'])}>
          {visiblePageAttributes[PageAttributeNames.returnToMerchantButton] ? (
            <AppButton
              className={styles['back-to-merchant-button']}
              isDisabled={returnToMerchantButtonDisabled}
              onClick={onReturnToMerchantButtonClick}
              label={t(LOCIZE_SUCCESS_TRANSLATION_KEYS.backToMerchant)}
            />
          ) : null}
          {everyPayProviderEnabled &&
          visiblePageAttributes[PageAttributeNames.standingOrderSection] ? (
            <AppButton
              className={styles['customer-profile-redirect-button']}
              variant={ButtonVariants.secondary}
              onClick={onCustomerProfileRedirectionButtonClick}
              label={t(
                isWithDownPayment
                  ? LOCIZE_SUCCESS_TRANSLATION_KEYS.makeDownpayment
                  : LOCIZE_SUCCESS_TRANSLATION_KEYS.signStandingPayment,
              )}
            />
          ) : null}
        </div>
      ) : null}
      {hasCreditAccount ? (
        <AppLocalizationComponent
          className={c(styles.paragraph, styles.margin)}
          locizeKey={tc(
            shouldShowDisclaimerWithCreditAmount
              ? LOCIZE_COMMON_TRANSLATION_KEYS.estoAccountDisclaimerWithCreditAmount
              : LOCIZE_COMMON_TRANSLATION_KEYS.estoAccountDisclaimer,
          )}
          values={{
            user_credit_account_limit: userCreditAccount?.credit_limit,
          }}
          components={{ make_bold: <AppBold /> }}
          t={t}
        />
      ) : null}
      {visiblePageAttributes[PageAttributeNames.estoAccountSection] ? (
        <AppButton
          onClick={onEstoAccountButtonClick}
          className={c(
            styles['account-button'],
            hasCreditAccount && styles['margin-top'],
          )}
          variant={ButtonVariants.white}
          label={tc(LOCIZE_COMMON_TRANSLATION_KEYS.estoAccount)}
        />
      ) : null}
    </div>
  );
};

export default Page;
