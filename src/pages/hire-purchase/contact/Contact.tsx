import { ContactForm } from 'components/contact-form';
import type { ContactFormType } from 'hooks/page-logic/hire-purchase';
import { useContactPageLogic } from 'hooks/page-logic/hire-purchase';
import type { UseFormReturn } from 'react-hook-form';

const Contact = () => {
  const {
    onContactFormSubmit,
    politicalExposureOptions,
    visiblePageAttributes,
    userInfoValidationErrors,
    form,
    conditionsAgreement,
  } = useContactPageLogic();

  const contextValue = {
    visiblePageAttributes,
    userInfoValidationErrors,
    politicalExposureOptions,
    showPoliticalExposureInfo: false, // hire-purchase doesn't show info tooltip
  };

  // Wrapper to match the expected signature
  const handleSubmit = async (
    data: ContactFormType,
    formInstance?: UseFormReturn<ContactFormType>,
  ) => {
    if (formInstance) {
      return onContactFormSubmit(data, formInstance);
    }
  };

  return (
    <ContactForm<ContactFormType>
      form={form}
      onSubmit={handleSubmit}
      contextValue={contextValue}
      passFormToSubmit={true}
      conditionsAgreement={conditionsAgreement}
    />
  );
};

export default Contact;
