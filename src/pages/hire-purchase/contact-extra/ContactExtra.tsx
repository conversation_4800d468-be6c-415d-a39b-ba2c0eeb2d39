import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import {
  AddCompanyDetailsField,
  ContactExtraFormProvider,
  EmploymentDateField,
  ExpenditureField,
  FutureReducedEarningsField,
  MonthlyLivingExpensesField,
  NetIncomeField,
  NumberOfDependentsField,
  OccupationCategoryField,
  OverdueDebtField,
  PlanningNewDebtsField,
  SpouseSendInstructions,
  UltimateBeneficialOwnerField,
} from 'components/contact-extra-form';
import { WarningNotification } from 'components/notification';
import { Button } from 'components/ui/button';
import { useRootContext } from 'context/root';
import { useContactExtraPageLogic } from 'hooks/page-logic/hire-purchase';
import { FormProvider } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

const ContactExtraPage = () => {
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const {
    form,
    onContactExtraFormSubmit,
    visiblePageAttributes,
    userInfoExtraValidationErrors,
    sendingConsentLinkValidationErrors,
    instructionsSent,
    setInstructionsSent,
    sendingConsentLink,
    occupationCategoryOptions,
    legalPeopleOptions,
    employmentDateOptions,
    addLegalPersonToInvoiceDisabled,
    legalPeopleLoading,
    handleSendConsentLink,
    onAddLegalPersonToInvoiceChange,
  } = useContactExtraPageLogic();

  return (
    <FormProvider {...form}>
      <ContactExtraFormProvider
        value={{
          visiblePageAttributes,
          validationErrors: userInfoExtraValidationErrors,
          occupationCategoryOptions,
          employmentDateOptions,
        }}
      >
        <form
          onSubmit={form.handleSubmit(onContactExtraFormSubmit)}
          className="grid w-full gap-2"
        >
          {visiblePageAttributes[PageAttributeNames.checkIncomeDisclaimer] ? (
            <WarningNotification className="mb-10">
              {tc(LOCIZE_COMMON_TRANSLATION_KEYS.checkIncomeDisclaimer2)}
            </WarningNotification>
          ) : null}

          <OccupationCategoryField />
          <NetIncomeField />
          <ExpenditureField />
          <MonthlyLivingExpensesField />
          <NumberOfDependentsField />
          <EmploymentDateField />
          <OverdueDebtField />
          <PlanningNewDebtsField />
          <FutureReducedEarningsField />
          <UltimateBeneficialOwnerField />

          <AddCompanyDetailsField
            form={form}
            isDisabled={addLegalPersonToInvoiceDisabled}
            legalPeopleLoading={legalPeopleLoading}
            onChange={onAddLegalPersonToInvoiceChange}
            selectOptions={legalPeopleOptions}
            visible={
              visiblePageAttributes[PageAttributeNames.addLegalPersonToInvoice]
            }
          />

          <SpouseSendInstructions
            form={form}
            onSendInstructions={handleSendConsentLink}
            sendingConsentLink={sendingConsentLink}
            validationErrors={sendingConsentLinkValidationErrors}
            instructionsSent={instructionsSent}
            onInstructionsSentChange={setInstructionsSent}
            visible={
              visiblePageAttributes[
                PageAttributeNames.spouseInstructionsSection
              ]
            }
          />

          <Button
            className="mt-12"
            disabled={form.formState.isSubmitting}
            loading={form.formState.isSubmitting}
            type="submit"
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
          </Button>
          <Button
            fullWidth
            className="mt-2"
            variant="white"
            loading={
              !form.formState.isSubmitting && pageUrlAndNavigationProcessing
            }
            disabled={form.formState.isSubmitting}
            onClick={() => getPageUrlAndNavigate(false)}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
          </Button>
        </form>
      </ContactExtraFormProvider>
    </FormProvider>
  );
};

export default ContactExtraPage;
