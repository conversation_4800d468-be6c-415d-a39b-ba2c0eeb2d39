import {
  LOCIZE_CHECKOUT_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Typography } from 'components/typography';
import type { CarouselApi } from 'components/ui/carousel';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from 'components/ui/carousel';
import { useRootContext } from 'context/root';
import { useGetSmallLoanCampaign } from 'hooks';
import { useGetApplicationByReferenceSuspense } from 'hooks/use-get-application-by-reference-suspense';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const SmallLoanOfferBanner = () => {
  const { user } = useRootContext();
  const [emblaApi, setEmblaApi] = useState<CarouselApi | null>(null);

  const { application } = (
    user
      ? useGetCurrentApplicationSuspense
      : useGetApplicationByReferenceSuspense
  )();

  const { t } = useTranslation(LocizeNamespaces.checkout);

  const keys = useGetSmallLoanCampaign(application);

  const specialOfferLocizeKeys = keys?.offersKeys ?? [];

  const autoplayIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!emblaApi) return;

    const scrollPrev = () => {
      emblaApi.scrollPrev();
    };

    if (autoplayIntervalRef.current) {
      clearInterval(autoplayIntervalRef.current);
    }

    autoplayIntervalRef.current = setInterval(scrollPrev, 5000);

    return () => {
      if (autoplayIntervalRef.current) {
        clearInterval(autoplayIntervalRef.current);
      }
    };
  }, [emblaApi, specialOfferLocizeKeys.length]);

  if (!user) {
    return null;
  }

  if (specialOfferLocizeKeys.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-row justify-center bg-system-green-800 w-full">
      <Carousel
        opts={{
          loop: true,
          duration: 30,
        }}
        orientation="vertical"
        className="w-full h-[2.5rem]"
        setApi={setEmblaApi}
      >
        <CarouselContent isInteractive>
          {specialOfferLocizeKeys.flatMap((offerKey, index) => [
            <CarouselItem key={`offer-${index}`}>
              <div className="flex flex-row gap-2 justify-center py-3 bg-system-green-800 w-full px-4">
                <Typography
                  variant="text-xs"
                  affects="semibold"
                  className="text-system-yellow-400 whitespace-nowrap"
                >
                  {t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanLimitedOffer)}
                </Typography>

                <Typography
                  variant="text-xs"
                  affects="semibold"
                  className="text-white truncate"
                >
                  {t(offerKey)}
                </Typography>
              </div>
            </CarouselItem>,

            <CarouselItem key={`apply-${index}`}>
              <div className="flex flex-row gap-2 justify-center py-3 bg-system-green-800 w-full">
                <Typography
                  variant="text-xs"
                  affects="semibold"
                  className="text-white"
                >
                  {t(
                    LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanLimitedOfferApplyNow,
                  )}
                </Typography>
              </div>
            </CarouselItem>,
          ])}
        </CarouselContent>
      </Carousel>
    </div>
  );
};
