import { HirePurchaseRoutePaths } from 'app-constants';
import { DefaultLoader } from 'components/default-loader/DefaultLoader';
import { SmallLoanProgressBar } from 'components/progress-bar/small-loan-progress-bar/SmallLoanProgressBar';
import { ScrollIntoView } from 'components/scroll-into-view';
import { useRootContext } from 'context/root';
import { useIsMobileView } from 'hooks/system';
import { Suspense } from 'react';
import Skeleton from 'react-loading-skeleton';
import { Outlet, useLocation } from 'react-router-dom';
import { cn } from 'utils/tailwind';
import { MainFooter } from 'widgets/footers/main-footer';
import { MainHeader } from 'widgets/headers/main-header';
import { ContainerLayout } from 'widgets/layouts/container-layout';
import { PageLayout } from 'widgets/layouts/page-layout/PageLayout';

import { ApprovedLoanAmountBlock } from './approved-loan-amount-block';
// import { HirePurchaseWithMobileHeaderRoutePaths } from './small-loan-info-page/config';
import { SmallLoanInfoPage } from './hire-purchase-info-page/ui/SmallLoanInfoPage';
import { SmallLoanOfferBanner } from './hire-purchase-info-page/ui/SmallLoanOfferBanner';
import { HirePurchaseHeader } from './ui/HirePurchaseHeader';

export const Root = () => {
  const { pathname } = useLocation();
  const isMobileView = useIsMobileView();
  const { user } = useRootContext();
  const isLoginPath = pathname.includes(HirePurchaseRoutePaths.LOGIN);
  const isCheckoutPath = pathname.includes(HirePurchaseRoutePaths.CHECKOUT);
  const isSigningUpPath = pathname.includes(HirePurchaseRoutePaths.SIGNING);
  const isStatusPath =
    pathname.includes(HirePurchaseRoutePaths.REJECT) ||
    pathname.includes(HirePurchaseRoutePaths.PENDING) ||
    pathname.includes(HirePurchaseRoutePaths.SUCCESS);

  // const [, , pagePath] = location.pathname.split('/');

  // const isMobileFullView =
  //   isMobileView &&
  //   HirePurchaseWithMobileHeaderRoutePaths.includes(
  //     pagePath as HirePurchaseRoutePaths,
  //   );

  const renderLeftBlock = () => {
    if (user?.id && isSigningUpPath) {
      return <ApprovedLoanAmountBlock />;
    }

    // if (isMobileFullView) {
    //   return null;
    // }

    return <SmallLoanInfoPage />;
  };

  return (
    <PageLayout
      withLeftContainer={isSigningUpPath}
      className={cn(
        'font-family-inter',
        isMobileView &&
          (isCheckoutPath || isSigningUpPath) &&
          '[&_#rightBody]:border-t [&_#rightBody]:border-solid [&_#rightBody]:border-neutral-200 [&_#rightBody]:rounded-t-xxl [&_#rightBody]:shadow-[0px_-5px_15px_0px_rgba(42,40,135,0.05)]',
        isSigningUpPath && '[&_#leftBody]:mb-auto',
      )}
      right={
        isStatusPath ? (
          <Outlet />
        ) : (
          <Suspense fallback={<DefaultLoader />}>
            {isMobileView && !isLoginPath ? (
              <HirePurchaseHeader className="w-full mb-2" />
            ) : null}

            <ScrollIntoView
              componentId={isSigningUpPath ? 'leftDualPanel' : 'rightDualPanel'}
            />

            <Outlet />
          </Suspense>
        )
      }
      left={
        isStatusPath ? null : (
          <Suspense fallback={<LeftBlockSkeleton />}>
            {renderLeftBlock()}
          </Suspense>
        )
      }
      rightHeader={
        <>
          {isMobileView ? null : (
            <ContainerLayout className="sticky top-0 z-10 bg-primary-white md:relative md:bg-transparent">
              <MainHeader className="bg-transparent pb-0" />
            </ContainerLayout>
          )}
          {(isCheckoutPath || isLoginPath || isSigningUpPath) &&
          isMobileView ? null : isMobileView ? (
            <SmallLoanOfferBanner />
          ) : (
            <SmallLoanProgressBar className="px-8" />
          )}
        </>
      }
      rightFooter={
        !isMobileView ? (
          <ContainerLayout className="mt-auto bg-neutral-50 md:bg-transparent">
            <MainFooter />
          </ContainerLayout>
        ) : null
      }
    />
  );
};

function LeftBlockSkeleton() {
  const { pathname } = useLocation();
  const isSigningUpPath = pathname.includes(HirePurchaseRoutePaths.SIGNING);

  return isSigningUpPath ? (
    <div className="w-full h-[27rem] flex flex-col mx-auto gap-4 mt-9 mb-6 max-w-[18rem] md:max-w-[22rem]">
      <Skeleton className="w-full h-[5rem] rounded-2xl" />
      <Skeleton className="w-full h-[20rem] rounded-2xl" />
    </div>
  ) : (
    <div className="md:size-full h-[15rem] -mb-8 md:mb-0">
      <Skeleton className="size-full" />
    </div>
  );
}
