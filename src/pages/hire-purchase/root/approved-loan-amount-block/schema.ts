import { FormFieldNames } from 'app-constants';
import { z } from 'zod';

export const LoanAmountDecreaseFormSchema = ({
  minLoanAmount,
  maxLoanAmount,
}: {
  minLoanAmount: number;
  maxLoanAmount: number;
}) =>
  z.object({
    [FormFieldNames.netTotal]: z.number().min(minLoanAmount).max(maxLoanAmount),
  });

export type LoanAmountFormType = z.infer<
  ReturnType<typeof LoanAmountDecreaseFormSchema>
>;
