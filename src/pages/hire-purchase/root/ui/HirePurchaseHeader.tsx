import {
  LOCIZE_SCHEDULE_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { HirePurchaseProgressBar } from 'components/progress-bar/hire-purchase-progress-bar/HirePurchaseProgressBar';
import { Typography } from 'components/typography';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';

type HirePurchaseHeaderProps = {
  className?: string;
};

export const HirePurchaseHeader = ({ className }: HirePurchaseHeaderProps) => {
  const { t } = useTranslation(LocizeNamespaces.schedule);

  return (
    <div className={cn(className, 'w-full')}>
      <Typography
        affects="bold"
        className="mb-4 text-[1.875rem] w-full max-w-100 flex flex-col"
      >
        {' '}
        {t(LOCIZE_SCHEDULE_TRANSLATION_KEYS.hirePurchaseTitle)}
      </Typography>
      <HirePurchaseProgressBar className="mb-4" />
    </div>
  );
};

export default HirePurchaseHeader;
