import {
  AppPinTypes,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { AppLoader, AppPinConfirmationComponent } from 'components';
import { useSigningPageContext } from 'context/hire-purchase';
import { useEffectOnce, useIsMobileDevice } from 'hooks';
import { useTranslation } from 'react-i18next';

let isSigning = false;
let isExecuted = false;

export const PinConfirmationView = () => {
  const {
    smartIdContractSignaturePollChallengeId,
    mobileIdContractSignaturePollChallengeId,
    onPinConfirmationCancel,
    signAppByMobileIdOrSmartId,
  } = useSigningPageContext();
  const isMobile = useIsMobileDevice();
  const { t } = useTranslation(LocizeNamespaces.signing);

  useEffectOnce(() => {
    const handleFocus = () => resignOnIosDeviceFocus();
    window.addEventListener('focus', handleFocus);

    const timeoutId = setTimeout(() => {
      signContract();
    }, 3000);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('focus', handleFocus);
    };
  });

  const signContract = () => {
    if (isExecuted || isSigning) {
      return;
    }

    isSigning = true;

    signAppByMobileIdOrSmartId()
      .then(() => {
        isExecuted = true;
      })
      .finally(() => {
        isSigning = false;
      });
  };

  const resignOnIosDeviceFocus = () => {
    if (isMobile) {
      signContract();
    }
  };

  const isCancelButtonVisible = !isExecuted && !isSigning;

  if (!isCancelButtonVisible) {
    return (
      <AppLoader
        isRelative
        delayedMessageTimeout={0}
        delayedMessageTitle={t(
          LOCIZE_SIGNING_TRANSLATION_KEYS.signingContractHeading,
        )}
        delayedMessageText={t(
          LOCIZE_SIGNING_TRANSLATION_KEYS.keepPageOpenDisclaimer,
        )}
      />
    );
  }

  return (
    <AppPinConfirmationComponent
      onCancel={onPinConfirmationCancel}
      pin={
        smartIdContractSignaturePollChallengeId ||
        mobileIdContractSignaturePollChallengeId
      }
      pinType={AppPinTypes.pin2}
      isCancelButtonVisible={isCancelButtonVisible}
    />
  );
};
