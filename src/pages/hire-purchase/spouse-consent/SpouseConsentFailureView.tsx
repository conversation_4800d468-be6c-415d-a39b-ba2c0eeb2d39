import {
  AppLanguages,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { REDIRECT_URLS } from 'app-constants';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { useSpouseConsentPageContext } from 'context/hire-purchase';
import CircleAlertIcon from 'icons/circle-alert.svg?react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { isAppLanguage } from 'utils';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

export const SpouseConsentFailureView = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.signing);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const { onTryAgainButtonClick } = useSpouseConsentPageContext();

  const [isSupportRedirecting, setIsSupportRedirecting] = useState(false);

  const onSupportButtonClick = () => {
    setIsSupportRedirecting(true);
    window.location.href =
      REDIRECT_URLS.frequentlyAskedQuestionsPageUrls[
        isAppLanguage(i18n.language) ? i18n.language : AppLanguages.en
      ];
  };

  return (
    <FullScreenLayout>
      <div className="flex flex-col items-center justify-center bg-rose-50 rounded-full p-4">
        <CircleAlertIcon />
      </div>
      <Typography className="mt-6 text-center" variant="m">
        {t(LOCIZE_SIGNING_TRANSLATION_KEYS.signingFailed)}
      </Typography>
      <Typography className="mt-4 text-center">
        {t(LOCIZE_SIGNING_TRANSLATION_KEYS.signingFailedDescription)}
      </Typography>

      <Button
        className="mt-4"
        fullWidth
        onClick={onTryAgainButtonClick}
        variant="black"
      >
        {tc(LOCIZE_COMMON_TRANSLATION_KEYS.tryAgain)}
      </Button>
      <Button
        className="mt-4"
        fullWidth
        onClick={onSupportButtonClick}
        variant="transparent"
        loading={isSupportRedirecting}
      >
        {tc(LOCIZE_COMMON_TRANSLATION_KEYS.support)}
      </Button>
    </FullScreenLayout>
  );
};
