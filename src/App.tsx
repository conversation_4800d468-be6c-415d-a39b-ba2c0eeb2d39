import { COMPANY_NAME } from 'app-constants';
import {
  AppLayout,
  A<PERSON><PERSON><PERSON><PERSON>,
  App<PERSON><PERSON>er,
  AppStepCounter,
  AppTestMode,
} from 'components';
import { Toaster } from 'components/ui/sonner';
import { NativeTranslateObserverContext } from 'context/native-translate-observer-context';
import { useRootContext } from 'context/root';
import { useGetPageTitle } from 'hooks/use-get-page-title';
import { Suspense } from 'react';
import { Helmet } from 'react-helmet-async';
import { isNewDesignProduct } from 'utils/isNewDesignProduct';

export const App = () => {
  const { userWasFetched, application, productType } = useRootContext();
  const pageTitle = useGetPageTitle(application);

  if (isNewDesignProduct(productType)) {
    return (
      <>
        <Helmet
          title={pageTitle}
          defaultTitle={COMPANY_NAME}
          titleTemplate={`%s | ${COMPANY_NAME}`}
        />
        <NativeTranslateObserverContext>
          <AppRouter />
        </NativeTranslateObserverContext>
        <Toaster />
      </>
    );
  }

  if (!userWasFetched) {
    return <AppLoader />;
  }

  return (
    <Suspense>
      <Helmet
        title={pageTitle}
        defaultTitle={COMPANY_NAME}
        titleTemplate={`%s | ${COMPANY_NAME}`}
      />
      <NativeTranslateObserverContext>
        <AppLayout>
          <AppStepCounter />
          <AppTestMode />
          <Suspense>
            <AppRouter />
          </Suspense>
        </AppLayout>
      </NativeTranslateObserverContext>
    </Suspense>
  );
};
